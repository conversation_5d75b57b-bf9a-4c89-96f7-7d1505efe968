"use client";

import { useState, useTransition, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";
import { useUser } from "@clerk/nextjs";
import { PhoneNumberResource } from "@clerk/types";
import { getClerkErrorMessage } from "@/lib/password";

// Make sure this utility correctly formats to E.164, or remove its usage before API calls.
import { formatPhoneNumber } from "@/lib/utils";

interface PhoneNumberFieldProps {
  initialPhoneNumber: string;
}

export default function PhoneNumberField({ initialPhoneNumber }: PhoneNumberFieldProps) {
  const { user, isLoaded } = useUser();
  const [phoneNumber, setPhoneNumber] = useState(initialPhoneNumber);
  const [isUpdating, startTransition] = useTransition();
  const [error, setError] = useState<string | null>(null);
  const [isVerifying, setIsVerifying] = useState(false);
  const [verificationCode, setVerificationCode] = useState("");
  const [pendingPhoneNumber, setPendingPhoneNumber] = useState<PhoneNumberResource | null>(null);

  useEffect(() => {
    setPhoneNumber(initialPhoneNumber);
  }, [initialPhoneNumber]);

  const handleUpdateAndVerify = () => {
    if (!user || !isLoaded) {
      toast.error("User not loaded.");
      return;
    }

    // Sanitize and format the phone number to E.164
    let formattedNumber = phoneNumber.trim().replace(/[\s-()]/g, '');

    if (formattedNumber.startsWith('07')) {
        formattedNumber = `+40${formattedNumber.substring(1)}`;
    } else if (!formattedNumber.startsWith('+')) {
        toast.error("Please enter the number in international format, e.g., +40712345678.");
        return;
    }

    if (formattedNumber === initialPhoneNumber) {
      return; // No change
    }

    setError(null);
    startTransition(async () => {
      try {
        const currentPhoneNumber = user.phoneNumbers?.[0];

        if (currentPhoneNumber && currentPhoneNumber.phoneNumber !== formattedNumber) {
          await currentPhoneNumber.destroy();
        }
        
        // --- FIX IS HERE ---
        // Ensure the parameter is `phoneNumber` (camelCase)
        console.log( formattedNumber);
        const newPhone = await user.createPhoneNumber({ phoneNumber: formattedNumber });
        // -------------------

        setPendingPhoneNumber(newPhone);
        await newPhone.prepareVerification();
        setIsVerifying(true);
        toast.info("A verification code has been sent to your phone number.");

      } catch (err) {
        console.error("Error updating phone number:", JSON.stringify(err, null, 2));
        toast.error(getClerkErrorMessage(err));
        setPhoneNumber(initialPhoneNumber); // Revert on error
      }
    });
  };

  const handleVerifyPhoneNumber = async () => {
    if (!pendingPhoneNumber || !verificationCode) {
      toast.error("Please enter the verification code.");
      return;
    }

    startTransition(async () => {
      try {
        const result = await pendingPhoneNumber.attemptVerification({ code: verificationCode });
        
        // Check if verification is complete
        if (result.verification.status === 'verified') {
            toast.success("Phone number verified successfully!");
            setIsVerifying(false);
            setPendingPhoneNumber(null);
            setVerificationCode("");
            // You might want to update the initialPhoneNumber state here upon success
        } else {
            // Handle cases like "expired" or other statuses if needed
            toast.error(`Verification failed with status: ${result.verification.status}`);
        }
      } catch (err) {
        toast.error(getClerkErrorMessage(err));
      }
    });
  };

  // --- JSX remains the same, just ensure onBlur or a button triggers handleUpdateAndVerify ---
  // For example, using a button is often more explicit than onBlur for this kind of multi-step flow.
  
  return (
    <div className="space-y-4">
      <Label htmlFor="phoneNumber">Phone Number</Label>
      <div className="flex items-center space-x-2">
        <Input
          id="phoneNumber"
          type="tel"
          value={phoneNumber}
          onChange={(e) => setPhoneNumber(e.target.value)}
          className={error ? "border-red-500" : ""}
          disabled={isUpdating || isVerifying}
          placeholder="+40712345678"
        />
        <Button onClick={handleUpdateAndVerify} disabled={isUpdating || isVerifying || phoneNumber === initialPhoneNumber}>
            {isUpdating ? <Loader2 className="h-4 w-4 animate-spin" /> : "Update"}
        </Button>
      </div>
      <p className="text-xs text-gray-500">
        International format: +40712345678 or local format: 0712345678
      </p>
      {error && <p className="text-sm text-red-600">{error}</p>}

      {isVerifying && pendingPhoneNumber && (
        <div className="border-t pt-6 mt-4">
          <h3 className="text-lg font-medium">Verify your Phone Number</h3>
          <p className="text-sm text-gray-500 mb-4">
            We sent a verification code to {pendingPhoneNumber.phoneNumber}. Please enter the code below.
          </p>
          <div className="flex items-center space-x-2">
            <Input
              value={verificationCode}
              onChange={(e) => setVerificationCode(e.target.value)}
              placeholder="Verification Code"
              disabled={isUpdating}
              className="max-w-xs"
            />
            <Button 
              type="button" 
              onClick={handleVerifyPhoneNumber} 
              disabled={isUpdating}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              {isUpdating ? <Loader2 className="h-4 w-4 animate-spin" /> : "Verify"}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}


// "use client";

// import { useState, useTransition, useEffect } from "react";
// import { Input } from "@/components/ui/input";
// import { Label } from "@/components/ui/label";
// import { Button } from "@/components/ui/button";
// import { toast } from "sonner";
// import { Loader2 } from "lucide-react";
// import { useUser } from "@clerk/nextjs";
// import { formatPhoneNumber } from "@/lib/utils";
// import { PhoneNumberResource } from "@clerk/types";
// import { getClerkErrorMessage } from "@/lib/password";

// interface PhoneNumberFieldProps {
//   initialPhoneNumber: string;
// }

// export default function PhoneNumberField({ initialPhoneNumber }: PhoneNumberFieldProps) {
//   const { user, isLoaded } = useUser();
//   const [phoneNumber, setPhoneNumber] = useState(initialPhoneNumber);
//   const [isUpdating, startTransition] = useTransition();
//   const [error, setError] = useState<string | null>(null);
//   const [isVerifying, setIsVerifying] = useState(false);
//   const [verificationCode, setVerificationCode] = useState("");
//   const [pendingPhoneNumber, setPendingPhoneNumber] = useState<PhoneNumberResource | null>(null);

//   useEffect(() => {
//     setPhoneNumber(initialPhoneNumber);
//   }, [initialPhoneNumber]);

//   const handleBlur = () => {
//     if (!user || !isLoaded) {
//       toast.error("Utilizatorul nu este autentificat sau datele nu sunt încărcate.");
//       return;
//     }

//     const currentPhoneNumber = user.phoneNumbers?.[0];
//     const newPhoneNumber = phoneNumber?.trim();

//     if (newPhoneNumber === initialPhoneNumber) {
//       setError(null);
//       return; // No change, do nothing
//     }

//     setError(null);
//     startTransition(async () => {
//       try {
//         if (newPhoneNumber) {
//           if (currentPhoneNumber && currentPhoneNumber.phoneNumber !== newPhoneNumber) {
//             // Phone number is different, so destroy the old one and create a new one
//             await currentPhoneNumber.destroy();
//             const newPhone = await user.createPhoneNumber({ phoneNumber: newPhoneNumber });
//             setPendingPhoneNumber(newPhone);
//             setIsVerifying(true);
//             toast.info("Un cod de verificare a fost trimis pe noul număr de telefon.");
//           } else if (!currentPhoneNumber) {
//             // No phone number exists, so create a new one
//             const newPhone = await user.createPhoneNumber({ phoneNumber: newPhoneNumber });
//             setPendingPhoneNumber(newPhone);
//             setIsVerifying(true);
//             toast.info("Un cod de verificare a fost trimis pe numărul de telefon.");
//           }
//         } else if (currentPhoneNumber) {
//           // Phone number field is empty, but a number exists, so remove it
//           await currentPhoneNumber.destroy();
//           toast.success("Numărul de telefon a fost eliminat.");
//           setPendingPhoneNumber(null);
//           setIsVerifying(false);
//         }
//       } catch (err) {
//         console.error("Error updating phone number:", JSON.stringify(err, null, 2));
//         toast.error(getClerkErrorMessage(err));
//         setPhoneNumber(initialPhoneNumber); // Revert on error
//       }
//     });
//   };

//   const handleVerifyPhoneNumber = async () => {
//     if (!pendingPhoneNumber || !verificationCode) {
//       toast.error("Introduceți codul de verificare.");
//       return;
//     }

//     startTransition(async () => {
//       try {
//         await pendingPhoneNumber.attemptVerification({ code: verificationCode });
//         toast.success("Numărul de telefon a fost verificat cu succes!");
//         setIsVerifying(false);
//         setPendingPhoneNumber(null);
//         setVerificationCode("");
//       } catch (err) {
//         toast.error(getClerkErrorMessage(err));
//       }
//     });
//   };

//   return (
//     <div className="space-y-2">
//       <Label htmlFor="phoneNumber">Număr de telefon</Label>
//       <div className="relative">
//         <Input
//           id="phoneNumber"
//           type="tel"
//           value={phoneNumber}
//           onChange={(e) => setPhoneNumber(e.target.value)}
//           onBlur={(e) => {
//             const formatted = formatPhoneNumber(e.target.value);
//             setPhoneNumber(formatted);
//             handleBlur();
//           }}
//           className={error ? "border-red-500" : ""}
//           disabled={isUpdating}
//           placeholder="+40712345678"
//         />
//         {isUpdating && (
//           <Loader2 className="absolute right-2 top-1/2 -translate-y-1/2 h-4 w-4 animate-spin text-gray-500" />
//         )}
//       </div>
//       <p className="text-xs text-gray-500">
//         Format internațional: +40712345678 sau format local: 0712345678
//       </p>
//       {user?.phoneNumbers?.[0] && user.phoneNumbers[0].verification?.status !== 'verified' && (
//         <p className="text-xs text-amber-600">
//           ⚠️ Numărul de telefon nu este verificat. Verificați SMS-urile pentru cod de confirmare.
//         </p>
//       )}
//       {error && <p className="text-sm text-red-600">{error}</p>}

//       {isVerifying && pendingPhoneNumber && (
//         <div className="border-t pt-6 mt-4">
//           <h3 className="text-lg font-medium">Verifică numărul de telefon</h3>
//           <p className="text-sm text-gray-500 mb-4">
//             Am trimis un cod de verificare la {pendingPhoneNumber.phoneNumber}. Introduceți codul mai jos.
//           </p>
//           <div className="flex items-center space-x-2">
//             <Input
//               value={verificationCode}
//               onChange={(e) => setVerificationCode(e.target.value)}
//               placeholder="Cod de verificare"
//               disabled={isUpdating}
//               className="max-w-xs"
//             />
//             <Button 
//               type="button" 
//               onClick={handleVerifyPhoneNumber} 
//               disabled={isUpdating}
//               className="bg-green-600 hover:bg-green-700 text-white"
//             >
//               {isUpdating ? <Loader2 className="h-4 w-4 animate-spin" /> : "Verifică"}
//             </Button>
//           </div>
//         </div>
//       )}
//     </div>
//   );
// }
