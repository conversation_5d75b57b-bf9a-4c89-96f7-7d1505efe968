
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  PrismaClientKnownRequestError,
  PrismaClientUnknownRequestError,
  PrismaClientRustPanicError,
  PrismaClientInitializationError,
  PrismaClientValidationError,
  getPrismaClient,
  sqltag,
  empty,
  join,
  raw,
  skip,
  Decimal,
  Debug,
  objectEnumValues,
  makeStrictEnum,
  Extensions,
  warnOnce,
  defineDmmfProperty,
  Public,
  getRuntime,
  createParam,
} = require('./runtime/library.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.11.1
 * Query Engine version: f40f79ec31188888a2e33acda0ecc8fd10a853a9
 */
Prisma.prismaVersion = {
  client: "6.11.1",
  engine: "f40f79ec31188888a2e33acda0ecc8fd10a853a9"
}

Prisma.PrismaClientKnownRequestError = PrismaClientKnownRequestError;
Prisma.PrismaClientUnknownRequestError = PrismaClientUnknownRequestError
Prisma.PrismaClientRustPanicError = PrismaClientRustPanicError
Prisma.PrismaClientInitializationError = PrismaClientInitializationError
Prisma.PrismaClientValidationError = PrismaClientValidationError
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = sqltag
Prisma.empty = empty
Prisma.join = join
Prisma.raw = raw
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = Extensions.getExtensionContext
Prisma.defineExtension = Extensions.defineExtension

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}




  const path = require('path')

/**
 * Enums
 */
exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  email: 'email',
  emailVerified: 'emailVerified',
  firstName: 'firstName',
  lastName: 'lastName',
  profileImage: 'profileImage',
  userAM: 'userAM',
  phoneNumber: 'phoneNumber',
  newsletterOptIn: 'newsletterOptIn',
  externalId: 'externalId',
  externalProvider: 'externalProvider',
  salutation: 'salutation',
  role: 'role',
  jobTitle: 'jobTitle',
  department: 'department',
  bio: 'bio',
  preferredLanguage: 'preferredLanguage',
  timezone: 'timezone',
  permissions: 'permissions',
  lastLoginAt: 'lastLoginAt',
  loginCount: 'loginCount',
  lastActivityAt: 'lastActivityAt',
  isActive: 'isActive',
  inactiveBy: 'inactiveBy',
  inactiveAt: 'inactiveAt',
  inactiveReason: 'inactiveReason',
  isSuspended: 'isSuspended',
  suspendedBy: 'suspendedBy',
  suspendedAt: 'suspendedAt',
  suspensionReason: 'suspensionReason',
  deletedAt: 'deletedAt',
  deletedBy: 'deletedBy',
  deletedReason: 'deletedReason',
  passwordEnabled: 'passwordEnabled',
  twoFactorEnabled: 'twoFactorEnabled',
  totpEnabled: 'totpEnabled',
  mfaEnabledAt: 'mfaEnabledAt',
  mfaDisabledAt: 'mfaDisabledAt',
  loginAttempts: 'loginAttempts',
  lockoutUntil: 'lockoutUntil',
  emailNotifications: 'emailNotifications',
  pushNotifications: 'pushNotifications',
  smsNotifications: 'smsNotifications',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  createdBy: 'createdBy',
  updatedBy: 'updatedBy'
};

exports.Prisma.UserSessionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  sessionToken: 'sessionToken',
  expiresAt: 'expiresAt',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  deviceId: 'deviceId',
  location: 'location',
  lastActiveAt: 'lastActiveAt',
  isRevoked: 'isRevoked',
  revokedReason: 'revokedReason',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserGroupScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  permissions: 'permissions',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  createdBy: 'createdBy',
  updatedBy: 'updatedBy'
};

exports.Prisma.UserNotificationScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  title: 'title',
  message: 'message',
  type: 'type',
  isRead: 'isRead',
  readAt: 'readAt',
  link: 'link',
  createdAt: 'createdAt'
};

exports.Prisma.UserAuditLogScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  action: 'action',
  entityType: 'entityType',
  entityId: 'entityId',
  details: 'details',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  performedBy: 'performedBy',
  createdAt: 'createdAt'
};

exports.Prisma.OrderScalarFieldEnum = {
  id: 'id',
  orderNumber: 'orderNumber',
  amount: 'amount',
  isPaid: 'isPaid',
  vin: 'vin',
  invoiceAM: 'invoiceAM',
  updatesEnabled: 'updatesEnabled',
  terms: 'terms',
  orderStatus: 'orderStatus',
  paymentStatus: 'paymentStatus',
  paymentMethod: 'paymentMethod',
  shippingMethod: 'shippingMethod',
  shipmentStatus: 'shipmentStatus',
  showroom: 'showroom',
  placedAt: 'placedAt',
  processedAt: 'processedAt',
  completedAt: 'completedAt',
  cancelledAt: 'cancelledAt',
  shippingProcessedAt: 'shippingProcessedAt',
  shippedAt: 'shippedAt',
  deliveredAt: 'deliveredAt',
  paidAt: 'paidAt',
  refundedAt: 'refundedAt',
  createdBy: 'createdBy',
  updatedBy: 'updatedBy',
  version: 'version',
  isActive: 'isActive',
  deletedAt: 'deletedAt',
  archivedAt: 'archivedAt',
  notes: 'notes',
  billingAddressId: 'billingAddressId',
  shippingAddressId: 'shippingAddressId',
  userId: 'userId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  hasReturns: 'hasReturns',
  hasServiceRequests: 'hasServiceRequests'
};

exports.Prisma.OrderItemScalarFieldEnum = {
  id: 'id',
  quantity: 'quantity',
  price: 'price',
  notes: 'notes',
  notesToInvoice: 'notesToInvoice',
  vinOrderItem: 'vinOrderItem',
  createdBy: 'createdBy',
  updatedBy: 'updatedBy',
  version: 'version',
  orderId: 'orderId',
  productId: 'productId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.WishlistScalarFieldEnum = {
  id: 'id',
  productCode: 'productCode',
  userId: 'userId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ShippingAddressScalarFieldEnum = {
  id: 'id',
  fullName: 'fullName',
  address: 'address',
  city: 'city',
  county: 'county',
  phoneNumber: 'phoneNumber',
  notes: 'notes',
  isDefault: 'isDefault',
  userId: 'userId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BillingAddressScalarFieldEnum = {
  id: 'id',
  fullName: 'fullName',
  companyName: 'companyName',
  address: 'address',
  city: 'city',
  county: 'county',
  cui: 'cui',
  bank: 'bank',
  iban: 'iban',
  isDefault: 'isDefault',
  userId: 'userId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BannerScalarFieldEnum = {
  id: 'id',
  title: 'title',
  subtitle: 'subtitle',
  imageUrl: 'imageUrl',
  mobileImageUrl: 'mobileImageUrl',
  callToAction: 'callToAction',
  buttonText: 'buttonText',
  description: 'description',
  url: 'url',
  placement: 'placement',
  position: 'position',
  width: 'width',
  height: 'height',
  backgroundColor: 'backgroundColor',
  textColor: 'textColor',
  textAlignment: 'textAlignment',
  targetAudience: 'targetAudience',
  deviceTarget: 'deviceTarget',
  startDate: 'startDate',
  endDate: 'endDate',
  isActive: 'isActive',
  impressions: 'impressions',
  clicks: 'clicks',
  conversionRate: 'conversionRate',
  createdBy: 'createdBy',
  updatedBy: 'updatedBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CategoryLevel1ScalarFieldEnum = {
  id: 'id',
  name: 'name',
  nameRO: 'nameRO',
  afisat: 'afisat',
  imageUrl: 'imageUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CategoryLevel2ScalarFieldEnum = {
  id: 'id',
  name: 'name',
  nameRO: 'nameRO',
  afisat: 'afisat',
  imageUrl: 'imageUrl',
  level1Id: 'level1Id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CategoryLevel3ScalarFieldEnum = {
  id: 'id',
  name: 'name',
  nameRO: 'nameRO',
  descriere: 'descriere',
  afisat: 'afisat',
  familyCode: 'familyCode',
  imageUrl: 'imageUrl',
  slug: 'slug',
  metaTitle: 'metaTitle',
  metaDescription: 'metaDescription',
  displayOrder: 'displayOrder',
  productCount: 'productCount',
  lastProductAdded: 'lastProductAdded',
  isActive: 'isActive',
  deletedAt: 'deletedAt',
  level2Id: 'level2Id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BrandScalarFieldEnum = {
  id: 'id',
  name: 'name',
  nameRO: 'nameRO',
  afisat: 'afisat',
  imageUrl: 'imageUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.VehicleModelScalarFieldEnum = {
  id: 'id',
  name: 'name',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProductClassScalarFieldEnum = {
  id: 'id',
  classCode: 'classCode',
  brandId: 'brandId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProductClassVehicleModelScalarFieldEnum = {
  productClassId: 'productClassId',
  vehicleModelId: 'vehicleModelId',
  createdAt: 'createdAt'
};

exports.Prisma.ProductScalarFieldEnum = {
  id: 'id',
  Material_Number: 'Material_Number',
  Net_Weight: 'Net_Weight',
  Description_Local: 'Description_Local',
  Base_Unit_Of_Measur: 'Base_Unit_Of_Measur',
  Cross_Plant: 'Cross_Plant',
  New_Material: 'New_Material',
  PretAM: 'PretAM',
  FinalPrice: 'FinalPrice',
  HasDiscount: 'HasDiscount',
  activeDiscountType: 'activeDiscountType',
  activeDiscountValue: 'activeDiscountValue',
  discountPercentage: 'discountPercentage',
  priceRange: 'priceRange',
  ImageUrl: 'ImageUrl',
  IsOnLandingPage: 'IsOnLandingPage',
  Material_Number_normalized: 'Material_Number_normalized',
  Description_Local_normalized: 'Description_Local_normalized',
  stockStatus: 'stockStatus',
  createdBy: 'createdBy',
  updatedBy: 'updatedBy',
  version: 'version',
  isActive: 'isActive',
  deletedAt: 'deletedAt',
  Parts_Class: 'Parts_Class',
  classId: 'classId',
  Material_Group: 'Material_Group',
  categoryLevel3Id: 'categoryLevel3Id',
  isServiceable: 'isServiceable',
  warrantyMonths: 'warrantyMonths',
  createdAt: 'createdAt',
  last_updated_at: 'last_updated_at'
};

exports.Prisma.PriceHistoryScalarFieldEnum = {
  id: 'id',
  productId: 'productId',
  oldPretAM: 'oldPretAM',
  newPretAM: 'newPretAM',
  oldFinalPrice: 'oldFinalPrice',
  newFinalPrice: 'newFinalPrice',
  reason: 'reason',
  source: 'source',
  createdBy: 'createdBy',
  createdAt: 'createdAt'
};

exports.Prisma.ProductHistoryScalarFieldEnum = {
  id: 'id',
  Material_Number: 'Material_Number',
  changes: 'changes',
  snapshot: 'snapshot',
  change_type: 'change_type',
  version: 'version',
  changed_by: 'changed_by',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DiscountScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  type: 'type',
  value: 'value',
  startDate: 'startDate',
  endDate: 'endDate',
  active: 'active',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProductDiscountScalarFieldEnum = {
  id: 'id',
  productId: 'productId',
  discountId: 'discountId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DiscountHistoryScalarFieldEnum = {
  id: 'id',
  discountId: 'discountId',
  changes: 'changes',
  snapshot: 'snapshot',
  change_type: 'change_type',
  version: 'version',
  changed_by: 'changed_by',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProductAttributeScalarFieldEnum = {
  id: 'id',
  Material_Number: 'Material_Number',
  key: 'key',
  value: 'value',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProductAttributeHistoryScalarFieldEnum = {
  id: 'id',
  Material_Number: 'Material_Number',
  changes: 'changes',
  snapshot: 'snapshot',
  change_type: 'change_type',
  version: 'version',
  changed_by: 'changed_by',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.OrderStatusHistoryScalarFieldEnum = {
  id: 'id',
  orderId: 'orderId',
  orderStatus: 'orderStatus',
  paymentStatus: 'paymentStatus',
  shipmentStatus: 'shipmentStatus',
  previousOrderStatus: 'previousOrderStatus',
  previousPaymentStatus: 'previousPaymentStatus',
  previousShipmentStatus: 'previousShipmentStatus',
  reason: 'reason',
  notes: 'notes',
  changedBy: 'changedBy',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  createdAt: 'createdAt'
};

exports.Prisma.ReturnScalarFieldEnum = {
  id: 'id',
  returnNumber: 'returnNumber',
  orderId: 'orderId',
  status: 'status',
  reason: 'reason',
  additionalNotes: 'additionalNotes',
  isApproved: 'isApproved',
  approvedBy: 'approvedBy',
  approvedAt: 'approvedAt',
  rejectionReason: 'rejectionReason',
  refundAmount: 'refundAmount',
  refundMethod: 'refundMethod',
  refundedAt: 'refundedAt',
  refundReference: 'refundReference',
  returnShippingLabel: 'returnShippingLabel',
  receivedAt: 'receivedAt',
  inspectedAt: 'inspectedAt',
  createdBy: 'createdBy',
  updatedBy: 'updatedBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ReturnItemScalarFieldEnum = {
  id: 'id',
  returnId: 'returnId',
  orderItemId: 'orderItemId',
  quantity: 'quantity',
  reason: 'reason',
  condition: 'condition',
  description: 'description',
  isReceived: 'isReceived',
  isInspected: 'isInspected',
  inspectionNotes: 'inspectionNotes',
  inspectionResult: 'inspectionResult',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ReturnStatusHistoryScalarFieldEnum = {
  id: 'id',
  returnId: 'returnId',
  previousStatus: 'previousStatus',
  newStatus: 'newStatus',
  notes: 'notes',
  changedBy: 'changedBy',
  createdAt: 'createdAt'
};

exports.Prisma.ServiceRequestScalarFieldEnum = {
  id: 'id',
  serviceNumber: 'serviceNumber',
  userId: 'userId',
  vin: 'vin',
  vehicleModel: 'vehicleModel',
  vehicleYear: 'vehicleYear',
  mileage: 'mileage',
  type: 'type',
  status: 'status',
  description: 'description',
  diagnosisNotes: 'diagnosisNotes',
  preferredDate: 'preferredDate',
  scheduledDate: 'scheduledDate',
  completedDate: 'completedDate',
  estimatedDuration: 'estimatedDuration',
  estimatedCost: 'estimatedCost',
  finalCost: 'finalCost',
  isPaid: 'isPaid',
  paidAt: 'paidAt',
  createdBy: 'createdBy',
  updatedBy: 'updatedBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ServiceItemScalarFieldEnum = {
  id: 'id',
  serviceRequestId: 'serviceRequestId',
  productId: 'productId',
  description: 'description',
  quantity: 'quantity',
  unitPrice: 'unitPrice',
  totalPrice: 'totalPrice',
  itemType: 'itemType',
  laborHours: 'laborHours',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ServiceStatusHistoryScalarFieldEnum = {
  id: 'id',
  serviceRequestId: 'serviceRequestId',
  previousStatus: 'previousStatus',
  newStatus: 'newStatus',
  notes: 'notes',
  changedBy: 'changedBy',
  createdAt: 'createdAt'
};

exports.Prisma.CategorySectionScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  image: 'image',
  href: 'href'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.NotificationType = exports.$Enums.NotificationType = {
  INFO: 'INFO',
  SUCCESS: 'SUCCESS',
  WARNING: 'WARNING',
  ERROR: 'ERROR'
};

exports.BannerPlacement = exports.$Enums.BannerPlacement = {
  HOME: 'HOME',
  CATEGORY: 'CATEGORY',
  PRODUCT: 'PRODUCT',
  CHECKOUT: 'CHECKOUT',
  SIDEBAR: 'SIDEBAR',
  HEADER: 'HEADER',
  FOOTER: 'FOOTER',
  POPUP: 'POPUP',
  HERO: 'HERO',
  CATEGORY_SECTION_LANDING_PAGE: 'CATEGORY_SECTION_LANDING_PAGE'
};

exports.DeviceTarget = exports.$Enums.DeviceTarget = {
  ALL: 'ALL',
  DESKTOP: 'DESKTOP',
  MOBILE: 'MOBILE',
  TABLET: 'TABLET'
};

exports.ChangeType = exports.$Enums.ChangeType = {
  CRON_DISCOUNT_EXPIRED: 'CRON_DISCOUNT_EXPIRED',
  MANUAL_DISCOUNT_EXPIRED_BUTTON_PRESS: 'MANUAL_DISCOUNT_EXPIRED_BUTTON_PRESS',
  INSERT: 'INSERT',
  UPDATE: 'UPDATE',
  DELETE: 'DELETE',
  ORDER: 'ORDER',
  DISCOUNT: 'DISCOUNT',
  PRODUCT_ADDED: 'PRODUCT_ADDED',
  PRODUCT_DELETE_ALL: 'PRODUCT_DELETE_ALL',
  PRODUCT_DELETE: 'PRODUCT_DELETE',
  PRODUCT_ADD_TO_DISCOUNT_WITH_CSV: 'PRODUCT_ADD_TO_DISCOUNT_WITH_CSV',
  ADDED_TO_DISCOUNT: 'ADDED_TO_DISCOUNT',
  DELETED_FROM_DISCOUNT: 'DELETED_FROM_DISCOUNT'
};

exports.DiscountType = exports.$Enums.DiscountType = {
  PERCENTAGE: 'PERCENTAGE',
  FIXED_AMOUNT: 'FIXED_AMOUNT',
  NEW_PRICE: 'NEW_PRICE'
};

exports.MisterMiss = exports.$Enums.MisterMiss = {
  Dl: 'Dl',
  Dna: 'Dna'
};

exports.Rol = exports.$Enums.Rol = {
  administAB: 'administAB',
  moderatorAB: 'moderatorAB',
  inregistratAB: 'inregistratAB',
  fourLvlAdminAB: 'fourLvlAdminAB',
  fourLvlInregistratAB: 'fourLvlInregistratAB',
  angajatAB: 'angajatAB'
};

exports.Showroom = exports.$Enums.Showroom = {
  CJ: 'CJ',
  BV: 'BV',
  TM: 'TM',
  AR: 'AR',
  BAC: 'BAC',
  BAN: 'BAN',
  OTP: 'OTP',
  MIL: 'MIL',
  TGM: 'TGM',
  JIL: 'JIL',
  CT: 'CT',
  CRA: 'CRA',
  SB: 'SB'
};

exports.OrderStatus = exports.$Enums.OrderStatus = {
  plasata: 'plasata',
  procesare: 'procesare',
  confirmata: 'confirmata',
  pregatita: 'pregatita',
  expediata: 'expediata',
  livrata: 'livrata',
  completa: 'completa',
  anulata: 'anulata',
  stornata: 'stornata',
  returnata: 'returnata',
  partiala: 'partiala'
};

exports.ShippingMethod = exports.$Enums.ShippingMethod = {
  curier: 'curier',
  showroom: 'showroom'
};

exports.ShipmentStatus = exports.$Enums.ShipmentStatus = {
  asteptare: 'asteptare',
  prelucrare: 'prelucrare',
  pregatit: 'pregatit',
  expediat: 'expediat',
  tranzit: 'tranzit',
  livrat: 'livrat',
  esuat: 'esuat',
  intors: 'intors',
  anulat: 'anulat',
  partial: 'partial'
};

exports.PaymentMethod = exports.$Enums.PaymentMethod = {
  ramburs: 'ramburs',
  card: 'card',
  transfer: 'transfer',
  laTermen: 'laTermen'
};

exports.PaymentStatus = exports.$Enums.PaymentStatus = {
  asteptare: 'asteptare',
  succes: 'succes',
  esuat: 'esuat',
  rambursat: 'rambursat',
  partial_rambursat: 'partial_rambursat',
  contestat: 'contestat'
};

exports.StockStatus = exports.$Enums.StockStatus = {
  IN_STOCK: 'IN_STOCK',
  LOW_STOCK: 'LOW_STOCK',
  OUT_OF_STOCK: 'OUT_OF_STOCK',
  DISCONTINUED: 'DISCONTINUED',
  UNKNOWN: 'UNKNOWN'
};

exports.ReturnStatus = exports.$Enums.ReturnStatus = {
  requested: 'requested',
  approved: 'approved',
  rejected: 'rejected',
  awaitingReceipt: 'awaitingReceipt',
  received: 'received',
  inspected: 'inspected',
  refundIssued: 'refundIssued',
  completed: 'completed',
  cancelled: 'cancelled'
};

exports.ReturnReason = exports.$Enums.ReturnReason = {
  wrongItem: 'wrongItem',
  defective: 'defective',
  damaged: 'damaged',
  notAsDescribed: 'notAsDescribed',
  noLongerWanted: 'noLongerWanted',
  other: 'other'
};

exports.ReturnItemReason = exports.$Enums.ReturnItemReason = {
  wrongItem: 'wrongItem',
  defective: 'defective',
  damaged: 'damaged',
  notAsDescribed: 'notAsDescribed',
  noLongerWanted: 'noLongerWanted',
  other: 'other'
};

exports.ItemCondition = exports.$Enums.ItemCondition = {
  asDescribed: 'asDescribed',
  damaged: 'damaged',
  opened: 'opened',
  used: 'used',
  missingParts: 'missingParts'
};

exports.InspectionResult = exports.$Enums.InspectionResult = {
  approved: 'approved',
  rejected: 'rejected',
  partiallyApproved: 'partiallyApproved'
};

exports.RefundMethod = exports.$Enums.RefundMethod = {
  originalPayment: 'originalPayment',
  storeCredit: 'storeCredit',
  bankTransfer: 'bankTransfer'
};

exports.ServiceType = exports.$Enums.ServiceType = {
  repair: 'repair',
  maintenance: 'maintenance',
  warranty: 'warranty',
  installation: 'installation',
  inspection: 'inspection',
  other: 'other'
};

exports.ServiceStatus = exports.$Enums.ServiceStatus = {
  requested: 'requested',
  scheduled: 'scheduled',
  inProgress: 'inProgress',
  diagnosisComplete: 'diagnosisComplete',
  awaitingParts: 'awaitingParts',
  awaitingApproval: 'awaitingApproval',
  completed: 'completed',
  cancelled: 'cancelled',
  delivered: 'delivered'
};

exports.ServiceItemType = exports.$Enums.ServiceItemType = {
  part: 'part',
  labor: 'labor',
  fee: 'fee',
  other: 'other'
};

exports.Prisma.ModelName = {
  User: 'User',
  UserSession: 'UserSession',
  UserGroup: 'UserGroup',
  UserNotification: 'UserNotification',
  UserAuditLog: 'UserAuditLog',
  Order: 'Order',
  OrderItem: 'OrderItem',
  Wishlist: 'Wishlist',
  ShippingAddress: 'ShippingAddress',
  BillingAddress: 'BillingAddress',
  Banner: 'Banner',
  CategoryLevel1: 'CategoryLevel1',
  CategoryLevel2: 'CategoryLevel2',
  CategoryLevel3: 'CategoryLevel3',
  Brand: 'Brand',
  VehicleModel: 'VehicleModel',
  ProductClass: 'ProductClass',
  ProductClassVehicleModel: 'ProductClassVehicleModel',
  Product: 'Product',
  PriceHistory: 'PriceHistory',
  ProductHistory: 'ProductHistory',
  Discount: 'Discount',
  ProductDiscount: 'ProductDiscount',
  DiscountHistory: 'DiscountHistory',
  ProductAttribute: 'ProductAttribute',
  ProductAttributeHistory: 'ProductAttributeHistory',
  OrderStatusHistory: 'OrderStatusHistory',
  Return: 'Return',
  ReturnItem: 'ReturnItem',
  ReturnStatusHistory: 'ReturnStatusHistory',
  ServiceRequest: 'ServiceRequest',
  ServiceItem: 'ServiceItem',
  ServiceStatusHistory: 'ServiceStatusHistory',
  CategorySection: 'CategorySection'
};
/**
 * Create the Client
 */
const config = {
  "generator": {
    "name": "client",
    "provider": {
      "fromEnvVar": null,
      "value": "prisma-client-js"
    },
    "output": {
      "value": "C:\\Users\\<USER>\\Documents\\JS-React\\pieseabv-frontend\\generated\\prisma",
      "fromEnvVar": null
    },
    "config": {
      "engineType": "library"
    },
    "binaryTargets": [
      {
        "fromEnvVar": null,
        "value": "windows",
        "native": true
      }
    ],
    "previewFeatures": [],
    "sourceFilePath": "C:\\Users\\<USER>\\Documents\\JS-React\\pieseabv-frontend\\prisma\\schema.prisma",
    "isCustomOutput": true
  },
  "relativeEnvPaths": {
    "rootEnvPath": null,
    "schemaEnvPath": "../../.env"
  },
  "relativePath": "../../prisma",
  "clientVersion": "6.11.1",
  "engineVersion": "f40f79ec31188888a2e33acda0ecc8fd10a853a9",
  "datasourceNames": [
    "db"
  ],
  "activeProvider": "postgresql",
  "postinstall": false,
  "inlineDatasources": {
    "db": {
      "url": {
        "fromEnvVar": "DATABASE_URL",
        "value": null
      }
    }
  },
  "inlineSchema": "// This is your Prisma schema file,\n// learn more about it in the docs: https://pris.ly/d/prisma-schema\n\n// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?\n// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init\n\ngenerator client {\n  provider = \"prisma-client-js\"\n  output   = \"../generated/prisma\"\n}\n\ndatasource db {\n  provider = \"postgresql\"\n  url      = env(\"DATABASE_URL\")\n}\n\nmodel User {\n  id              String    @id @default(cuid())\n  email           String    @unique\n  emailVerified   DateTime?\n  firstName       String\n  lastName        String\n  profileImage    String\n  userAM          String?   @unique\n  phoneNumber     String?\n  newsletterOptIn Boolean   @default(false)\n\n  // Authentication and identity\n  externalId       String? @unique // ID from Clerk/Kinde\n  externalProvider String? // \"clerk\", \"kinde\", etc.\n\n  // User profile\n  salutation        MisterMiss?\n  role              Rol         @default(inregistratAB)\n  jobTitle          String?\n  department        String?\n  bio               String?     @db.Text\n  preferredLanguage String? // For localization\n  timezone          String? // User's timezone\n\n  // Permissions and access control\n  permissions  String[] // Array of permission keys\n  accessGroups UserGroup[] @relation(\"UserToGroup\")\n\n  // User activity tracking\n  lastLoginAt    DateTime?\n  loginCount     Int       @default(0)\n  lastActivityAt DateTime?\n\n  // Account activation\n  isActive       Boolean   @default(true)\n  inactiveBy     String? // User ID or system ID that deactivated this user\n  inactiveAt     DateTime?\n  inactiveReason String?   @db.Text\n\n  // Account status\n  isSuspended      Boolean   @default(false)\n  suspendedBy      String? // User ID or system ID that suspended this user\n  suspendedAt      DateTime? @default(now())\n  suspensionReason String?   @db.Text\n\n  // Soft delete\n  deletedAt     DateTime?\n  deletedBy     String? // User ID or system ID that deleted this user\n  deletedReason String?   @db.Text\n\n  // Account security\n  passwordEnabled  Boolean   @default(false)\n  twoFactorEnabled Boolean   @default(false)\n  totpEnabled      Boolean   @default(false)\n  mfaEnabledAt     DateTime?\n  mfaDisabledAt    DateTime?\n\n  // Login security\n  loginAttempts Int       @default(0)\n  lockoutUntil  DateTime?\n\n  // Notification preferences\n  emailNotifications Boolean @default(true)\n  pushNotifications  Boolean @default(false)\n  smsNotifications   Boolean @default(false)\n\n  // Relations\n  orders            Order[]\n  wishlist          Wishlist[]\n  shippingAddresses ShippingAddress[]\n  billingAddresses  BillingAddress[]\n  sessions          UserSession[]\n  serviceRequests   ServiceRequest[]\n  notifications     UserNotification[]\n  auditLogs         UserAuditLog[]\n\n  // Metadata\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n  createdBy String? // User ID or system ID that created this user\n  updatedBy String? // User ID or system ID that last updated this user\n\n  @@index([email]) // Good for fast lookups by email\n  @@index([lastName, firstName]) // Useful for alphabetical sorting or searching by name\n  @@index([phoneNumber]) // For frequently search/validate by phoneNumber\n  @@index([lastLoginAt])\n  @@index([isActive, deletedAt])\n  @@index([externalId])\n  @@index([role])\n  @@index([lastActivityAt])\n  @@index([isSuspended])\n}\n\nmodel UserSession {\n  id     String @id @default(cuid())\n  userId String\n  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)\n\n  sessionToken  String    @unique\n  expiresAt     DateTime\n  ipAddress     String?\n  userAgent     String?\n  deviceId      String?\n  location      String? // Geo location info\n  lastActiveAt  DateTime?\n  isRevoked     Boolean   @default(false)\n  revokedReason String?\n\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n\n  @@index([sessionToken])\n  @@index([userId, expiresAt])\n  @@index([isRevoked])\n}\n\nmodel UserGroup {\n  id          String   @id @default(cuid())\n  name        String   @unique\n  description String?  @db.Text\n  permissions String[] // Array of permission keys\n\n  users User[] @relation(\"UserToGroup\")\n\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n  createdBy String?\n  updatedBy String?\n}\n\nmodel UserNotification {\n  id     String @id @default(cuid())\n  userId String\n  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)\n\n  title   String\n  message String           @db.Text\n  type    NotificationType\n  isRead  Boolean          @default(false)\n  readAt  DateTime?\n  link    String? // Optional link to navigate to\n\n  createdAt DateTime @default(now())\n\n  @@index([userId, isRead])\n  @@index([userId, createdAt])\n}\n\nmodel UserAuditLog {\n  id     String  @id @default(cuid())\n  userId String?\n  user   User?   @relation(fields: [userId], references: [id], onDelete: SetNull)\n\n  action      String // e.g., \"user.login\", \"user.update\", \"order.create\"\n  entityType  String // e.g., \"user\", \"order\", \"product\"\n  entityId    String? // ID of the affected entity\n  details     String? @db.Text // JSON string with details\n  ipAddress   String?\n  userAgent   String?\n  performedBy String? // User ID or system ID that performed the action\n\n  createdAt DateTime @default(now())\n\n  @@index([userId])\n  @@index([action])\n  @@index([entityType, entityId])\n  @@index([createdAt])\n}\n\nenum NotificationType {\n  INFO\n  SUCCESS\n  WARNING\n  ERROR\n}\n\nmodel Order {\n  id             String  @id @default(cuid())\n  orderNumber    String  @unique // Human-readable order number (e.g., ORD-2023-00001)\n  amount         Decimal @db.Decimal(15, 2)\n  isPaid         Boolean @default(false)\n  vin            String?\n  invoiceAM      String?\n  updatesEnabled Boolean @default(true)\n  terms          Boolean @default(true)\n\n  // Current status (denormalized for quick access)\n  orderStatus    OrderStatus    @default(plasata)\n  paymentStatus  PaymentStatus  @default(asteptare)\n  paymentMethod  PaymentMethod  @default(ramburs)\n  shippingMethod ShippingMethod @default(curier)\n  shipmentStatus ShipmentStatus @default(asteptare)\n  showroom       Showroom?\n\n  // Timestamps for key status changes (denormalized for quick access)\n  placedAt    DateTime  @default(now())\n  processedAt DateTime?\n  completedAt DateTime?\n  cancelledAt DateTime?\n\n  // Shipping timestamps\n  shippingProcessedAt DateTime?\n  shippedAt           DateTime?\n  deliveredAt         DateTime?\n\n  // Payment timestamps\n  paidAt     DateTime?\n  refundedAt DateTime?\n\n  // Audit fields\n  createdBy String?\n  updatedBy String?\n  version   Int     @default(1)\n\n  // Soft delete\n  isActive   Boolean   @default(true)\n  deletedAt  DateTime?\n  archivedAt DateTime?\n\n  // Relations\n  orderItems    OrderItem[]\n  statusHistory OrderStatusHistory[]\n  notes         String?\n\n  billingAddressId  String?\n  billingAddress    BillingAddress?  @relation(fields: [billingAddressId], references: [id], onDelete: Restrict)\n  shippingAddressId String?\n  shippingAddress   ShippingAddress? @relation(fields: [shippingAddressId], references: [id], onDelete: Restrict)\n  userId            String\n  user              User             @relation(fields: [userId], references: [id], onDelete: Restrict)\n\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n\n  // Add these relations\n  returns         Return[]\n  serviceRequests ServiceRequest[]\n\n  // Add these fields\n  hasReturns         Boolean @default(false)\n  hasServiceRequests Boolean @default(false)\n\n  @@index([orderNumber])\n  @@index([userId])\n  @@index([orderStatus])\n  @@index([createdAt])\n  @@index([placedAt, orderStatus])\n  @@index([userId, orderStatus, createdAt])\n  @@index([orderStatus, paymentStatus])\n  @@index([isActive, deletedAt])\n  @@index([archivedAt])\n  @@index([hasReturns])\n  @@index([hasServiceRequests])\n}\n\nmodel OrderItem {\n  id       String  @id @default(cuid())\n  quantity Int // @check(\"quantity > 0\") - Add in migration\n  price    Decimal @db.Decimal(10, 2) // @check(\"price >= 0\") - Add in migration\n\n  notes          String?\n  notesToInvoice Boolean @default(false)\n  vinOrderItem   String?\n\n  // Audit fields\n  createdBy String?\n  updatedBy String?\n  version   Int     @default(1)\n\n  orderId   String\n  order     Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)\n  productId String  @default(cuid())\n  product   Product @relation(fields: [productId], references: [id], onDelete: Restrict)\n\n  createdAt  DateTime     @default(now())\n  updatedAt  DateTime     @updatedAt\n  ReturnItem ReturnItem[]\n\n  @@unique([orderId, productId]) // Crucial for preventing duplicate items in an order\n  @@index([orderId])\n  @@index([productId])\n  @@index([orderId, createdAt]) // For order item chronology\n  @@index([updatedBy])\n  @@index([version])\n}\n\nmodel Wishlist {\n  id String @id @default(cuid())\n\n  productCode String\n  product     Product @relation(fields: [productCode], references: [Material_Number], onDelete: Cascade)\n  userId      String\n  user        User    @relation(fields: [userId], references: [id], onDelete: Cascade)\n\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n\n  @@unique([userId, productCode]) // Ensures a user can only have one of each product in their wishlist\n  @@index([userId]) // Useful for fetching all items in a user's wishlist\n}\n\nmodel ShippingAddress {\n  id          String  @id @default(cuid())\n  fullName    String\n  address     String\n  city        String\n  county      String\n  phoneNumber String\n  notes       String?\n  isDefault   Boolean @default(false)\n\n  orders Order[]\n  userId String\n  user   User    @relation(fields: [userId], references: [id], onDelete: Cascade)\n\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n\n  @@index([phoneNumber])\n}\n\nmodel BillingAddress {\n  id          String  @id @default(cuid())\n  fullName    String\n  companyName String?\n  address     String\n  city        String\n  county      String\n  cui         String?\n  bank        String?\n  iban        String?\n  isDefault   Boolean @default(false)\n\n  orders Order[]\n  userId String\n  user   User    @relation(fields: [userId], references: [id], onDelete: Cascade)\n\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n\n  @@index([cui])\n  @@index([iban]) // Useful for bank details lookup\n}\n\nmodel Banner {\n  id             String  @id @default(cuid())\n  title          String\n  subtitle       String?\n  imageUrl       String\n  mobileImageUrl String? // Separate image for mobile devices\n  callToAction   String?\n  buttonText     String? // Text to display on the CTA button\n  description    String?\n  url            String?\n\n  // Banner placement and display options\n  placement       BannerPlacement @default(HOME)\n  position        Int             @default(0) // Order of display within placement\n  width           String? // CSS width value (e.g., \"100%\", \"500px\")\n  height          String? // CSS height value\n  backgroundColor String? // Background color in hex or CSS color name\n  textColor       String? // Text color in hex or CSS color name\n  textAlignment   String? // Text alignment (left, center, right)\n\n  // Targeting options\n  targetAudience String? // JSON string for audience targeting rules\n  deviceTarget   DeviceTarget @default(ALL)\n\n  // Scheduling\n  startDate DateTime  @default(now())\n  endDate   DateTime?\n  isActive  Boolean   @default(true)\n\n  // Analytics\n  impressions    Int    @default(0)\n  clicks         Int    @default(0)\n  conversionRate Float? // Calculated field\n\n  // Metadata\n  createdBy String?\n  updatedBy String?\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n\n  @@index([placement, position, isActive])\n  @@index([startDate, endDate])\n  @@index([deviceTarget])\n}\n\nenum BannerPlacement {\n  HOME\n  CATEGORY\n  PRODUCT\n  CHECKOUT\n  SIDEBAR\n  HEADER\n  FOOTER\n  POPUP\n  HERO\n  CATEGORY_SECTION_LANDING_PAGE\n}\n\nenum DeviceTarget {\n  ALL\n  DESKTOP\n  MOBILE\n  TABLET\n}\n\n//Categorii\nmodel CategoryLevel1 {\n  id       String  @id @default(cuid())\n  name     String  @unique // Numele categoriei de nivel 1 (ex: \"Repair\")\n  nameRO   String? // Numele în română (opțional)\n  afisat   Boolean @default(false) // Indică dacă categoria este afișată în Mega Menu\n  imageUrl String? // URL-ul imaginii pentru categoria de nivel 1\n\n  level2Categories CategoryLevel2[] // Relație către nivelul 2\n\n  createdAt DateTime @default(now())\n  updatedAt DateTime @default(now())\n}\n\nmodel CategoryLevel2 {\n  id       String  @id @default(cuid())\n  name     String // Numele categoriei de nivel 2 (ex: \"Standard parts\")\n  nameRO   String? // Numele în română (opțional)\n  afisat   Boolean @default(false) // Indică dacă categoria este afișată în Mega Menu\n  imageUrl String? // URL-ul imaginii pentru categoria de nivel 2\n\n  level1Id         String\n  level1           CategoryLevel1   @relation(fields: [level1Id], references: [id])\n  level3Categories CategoryLevel3[] // Relație către nivelul 3\n\n  createdAt DateTime @default(now())\n  updatedAt DateTime @default(now())\n\n  @@unique([name, level1Id]) // O categorie de nivel 2 este unică în cadrul părintelui său de nivel 1\n}\n\nmodel CategoryLevel3 {\n  id         String  @id @default(cuid())\n  name       String // Numele categoriei de nivel 3 (ex: \"Bolts\")\n  nameRO     String? // Numele în română (opțional)\n  descriere  String? // Descrierea scurta acategoriei\n  afisat     Boolean @default(false) // Indică dacă categoria este afișată în Mega Menu\n  familyCode String? @unique // Cheia din `categs.csv` și `allParts.csv`\n  imageUrl   String? // URL-ul imaginii pentru categoria de nivel 3\n\n  // SEO and display fields\n  slug            String? @unique // URL-friendly name for SEO : https://yourdomain.com/categories/{brake-pads}\n  metaTitle       String? // Title tag for SEO: BMW Engine Parts | YourSite, MINI Brake Pads | YourSite\n  metaDescription String? // Meta description for SEO: Shop our selection of high-quality BMW engine parts with fast delivery and warranty.\n  displayOrder    Int     @default(0)\n\n  // Category statistics (denormalized for performance)\n  productCount     Int       @default(0)\n  lastProductAdded DateTime?\n\n  // Audit fields\n  isActive  Boolean   @default(true) //controle whether the category is active in the system as awhole\n  deletedAt DateTime?\n\n  level2Id String\n  level2   CategoryLevel2 @relation(fields: [level2Id], references: [id])\n  products Product[] // Relație către produse\n\n  createdAt DateTime @default(now())\n  updatedAt DateTime @default(now())\n\n  @@index([familyCode]) // Index pentru căutări rapide după familyCode\n  @@index([slug])\n  @@index([displayOrder])\n  @@index([productCount])\n  @@index([isActive, deletedAt])\n}\n\n// --- Branduri și Modele ---\nmodel Brand {\n  id       String  @id @default(cuid())\n  name     String  @unique // Ex: \"BMW\", \"MINI\", \"MOTO\"\n  nameRO   String? // Numele în română (opțional)\n  afisat   Boolean @default(true) // Indică dacă brandul este afișat în Mega Menu\n  imageUrl String? // URL-ul imaginii pentru brand\n\n  productClasses ProductClass[] // Relație către clasele de produse asociate\n\n  createdAt DateTime @default(now())\n  updatedAt DateTime @default(now())\n}\n\nmodel VehicleModel {\n  id   String @id @default(cuid())\n  name String @unique // Ex: F34\", \"R55\", \"K70\"\n\n  productClasses ProductClassVehicleModel[] // Relație many-to-many prin tabela de legătură\n\n  createdAt DateTime @default(now())\n  updatedAt DateTime @default(now())\n}\n\n// --- Clase de Produse (legătura între Parts Class, Brand și Modele) ---\n\nmodel ProductClass {\n  id        String @id @default(cuid())\n  classCode String @unique // Cheia din `class.csv` și `allParts.csv` (Parts Class)\n\n  brandId String\n  brand   Brand  @relation(fields: [brandId], references: [id])\n\n  vehicleModels ProductClassVehicleModel[] // Relație many-to-many prin tabela de legătură\n  products      Product[] // Relație către produsele din această clasă\n\n  createdAt DateTime @default(now())\n  updatedAt DateTime @default(now())\n\n  @@index([classCode]) // Index pentru căutări rapide după classCode\n}\n\n// Tabela de legătură Many-to-Many între ProductClass și VehicleModel\nmodel ProductClassVehicleModel {\n  productClassId String\n  vehicleModelId String\n\n  productClass ProductClass @relation(fields: [productClassId], references: [id])\n  vehicleModel VehicleModel @relation(fields: [vehicleModelId], references: [id])\n\n  createdAt DateTime @default(now())\n\n  @@id([productClassId, vehicleModelId]) // Cheie primară compusă\n}\n\nmodel Product {\n  id              String @id @default(cuid())\n  Material_Number String @unique // Material din `allParts.csv`\n\n  Net_Weight          String?\n  Description_Local   String?\n  Base_Unit_Of_Measur String?\n  Cross_Plant         String?\n  New_Material        String?\n\n  PretAM              Decimal?      @db.Decimal(15, 2) // @check(\"PretAM >= 0\") - Add in migration\n  FinalPrice          Decimal?      @db.Decimal(15, 2) // @check(\"FinalPrice >= 0\") - Add in migration\n  HasDiscount         Boolean       @default(false)\n  activeDiscountType  DiscountType? // The type of discount currently applied   PERCENTAGE   // DiscountValue is a percentage (e.g., 10 for 10%)   FIXED_AMOUNT // DiscountValue is a fixed amount to subtract (e.g., 5 for $5 off)   NEW_PRICE    // DiscountValue is the new absolute price for the product\n  activeDiscountValue Decimal?      @db.Decimal(10, 2) // The actual discount value (percentage, amount, or new price)\n  discountPercentage  Decimal?      @db.Decimal(5, 2) // Calculated percentage off for efficient filtering\n  priceRange          String? // e.g., \"0-50\", \"50-100\", \"100-200\", \"200+\"\n\n  ImageUrl        String[] @default([\"https://op47vimj99.ufs.sh/f/6Hnm5nafTbm964jRPnfTbm9EeHnDOzysS6K5X27Upql8xtjN\"]) // URL-ul imaginii pentru produs\n  IsOnLandingPage Boolean  @default(false)\n\n  // Search optimization fields\n  Material_Number_normalized   String? // Lowercase, trimmed version for search\n  Description_Local_normalized String? // For case-insensitive search\n\n  // Stock and availability\n  stockStatus StockStatus @default(UNKNOWN)\n\n  // Audit and versioning\n  createdBy String?\n  updatedBy String?\n  version   Int     @default(1)\n\n  // Soft delete\n  isActive  Boolean   @default(true)\n  deletedAt DateTime?\n\n  attributes              ProductAttribute[]\n  productHistory          ProductHistory[] // Relație către istoricul produsului\n  productAttributeHistory ProductAttributeHistory[] // Relație către istoricul produsului\n  discounts               ProductDiscount[]\n  orderItems              OrderItem[]\n  wishlist                Wishlist[]\n  PriceHistory            PriceHistory[]\n\n  Parts_Class  String?       @default(\"undefined-class\")\n  classId      String? // This will store the 'id' of the ProductClass\n  productClass ProductClass? @relation(fields: [classId], references: [id], onDelete: SetNull)\n\n  Material_Group   String?         @default(\"undefined-category\")\n  categoryLevel3Id String? // This will store the 'id' of the CategoryLevel3\n  categoryLevel3   CategoryLevel3? @relation(fields: [categoryLevel3Id], references: [id], onDelete: SetNull)\n\n  // Add these relations\n  serviceItems ServiceItem[]\n\n  // Add these fields\n  isServiceable  Boolean @default(false)\n  warrantyMonths Int?\n\n  createdAt       DateTime @default(now())\n  last_updated_at DateTime @default(now())\n\n  @@index([HasDiscount, FinalPrice]) // For filtering discounted products and sorting by price\n  @@index([categoryLevel3Id, HasDiscount]) // For category + discount filtering\n  @@index([categoryLevel3Id, FinalPrice]) // For category + price sorting\n  @@index([priceRange, HasDiscount]) // For price range filtering with discounts\n  @@index([discountPercentage]) // For sorting by discount percentage\n  @@index([Material_Number]) // Index pentru căutări/update-uri rapide după SKU\n  @@index([Material_Group])\n  @@index([Parts_Class])\n  @@index([Material_Group, Parts_Class]) // For filtering by category and class\n  @@index([categoryLevel3Id, Material_Group]) // For category-based queries\n  @@index([PretAM, HasDiscount]) // For price-based filtering\n  @@index([IsOnLandingPage, categoryLevel3Id]) // For landing page products by category\n  @@index([createdAt, categoryLevel3Id]) // For time-based category queries\n  @@index([Material_Number_normalized])\n  @@index([Description_Local_normalized])\n  @@index([stockStatus])\n  @@index([isActive, deletedAt])\n  @@index([updatedBy])\n  @@index([version])\n}\n\nmodel PriceHistory {\n  id        String  @id @default(cuid())\n  productId String\n  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)\n\n  oldPretAM     Decimal? @db.Decimal(15, 2)\n  newPretAM     Decimal? @db.Decimal(15, 2)\n  oldFinalPrice Decimal? @db.Decimal(15, 2)\n  newFinalPrice Decimal? @db.Decimal(15, 2)\n\n  reason String? // e.g., \"Weekly update\", \"Discount applied\", etc.\n  source String? // e.g., \"CSV import\", \"Manual adjustment\", etc.\n\n  createdBy String?\n  createdAt DateTime @default(now())\n\n  @@index([productId])\n  @@index([productId, createdAt])\n  @@index([createdAt])\n}\n\nmodel ProductHistory {\n  id              String     @id @default(uuid()) @db.Uuid\n  Material_Number String\n  changes         Json\n  snapshot        Json\n  change_type     ChangeType\n  version         Int\n  changed_by      String\n\n  createdAt DateTime @default(now())\n  updatedAt DateTime @default(now())\n\n  product Product @relation(fields: [Material_Number], references: [Material_Number], onDelete: Cascade)\n}\n\nmodel Discount {\n  id          String       @id @default(cuid())\n  name        String       @unique\n  description String\n  type        DiscountType\n  value       Decimal      @db.Decimal(10, 2)\n  startDate   DateTime\n  endDate     DateTime\n  active      Boolean      @default(false)\n  createdBy   String\n\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n\n  productDiscounts ProductDiscount[]\n  discountHistory  DiscountHistory[]\n\n  @@index([active])\n  @@index([name])\n}\n\nmodel ProductDiscount {\n  id String @id @default(cuid())\n\n  productId  String   @unique // This ensures one product can only have one discount\n  product    Product  @relation(fields: [productId], references: [id])\n  discountId String\n  discount   Discount @relation(fields: [discountId], references: [id])\n\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n\n  @@index([discountId]) // Keep an index on discountId for queries\n}\n\nmodel DiscountHistory {\n  id          String     @id @default(cuid())\n  discountId  String\n  changes     Json\n  snapshot    Json\n  change_type ChangeType\n  version     Int\n  changed_by  String\n\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n\n  discount Discount @relation(fields: [discountId], references: [id])\n}\n\nmodel ProductAttribute {\n  id              String  @id @default(cuid())\n  Material_Number String\n  key             String?\n  value           String?\n\n  product Product @relation(fields: [Material_Number], references: [Material_Number])\n\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n\n  @@unique([Material_Number, key])\n  @@index([key, value])\n  @@index([Material_Number])\n}\n\nmodel ProductAttributeHistory {\n  id              String     @id @default(cuid())\n  Material_Number String\n  changes         Json\n  snapshot        Json\n  change_type     ChangeType\n  version         Int\n  changed_by      String\n\n  product Product @relation(fields: [Material_Number], references: [Material_Number], onDelete: Cascade)\n\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n\n  @@index([Material_Number])\n}\n\nenum ChangeType {\n  CRON_DISCOUNT_EXPIRED\n  MANUAL_DISCOUNT_EXPIRED_BUTTON_PRESS\n  INSERT\n  UPDATE\n  DELETE\n  ORDER\n  DISCOUNT\n  PRODUCT_ADDED\n  PRODUCT_DELETE_ALL\n  PRODUCT_DELETE\n  PRODUCT_ADD_TO_DISCOUNT_WITH_CSV\n  ADDED_TO_DISCOUNT\n  DELETED_FROM_DISCOUNT\n}\n\nenum DiscountType {\n  PERCENTAGE // DiscountValue is a percentage (e.g., 10 for 10%)\n  FIXED_AMOUNT // DiscountValue is a fixed amount to subtract (e.g., 5 for $5 off)1\n  NEW_PRICE // DiscountValue is the new absolute price for the product\n}\n\nenum MisterMiss {\n  Dl\n  Dna\n}\n\nenum Rol {\n  administAB\n  moderatorAB\n  inregistratAB\n  fourLvlAdminAB\n  fourLvlInregistratAB\n  angajatAB\n}\n\nenum Showroom {\n  CJ\n  BV\n  TM\n  AR\n  BAC\n  BAN\n  OTP\n  MIL\n  TGM\n  JIL\n  CT\n  CRA\n  SB\n}\n\nenum OrderStatus {\n  plasata // Placed\n  procesare // Processing\n  confirmata // Confirmed\n  pregatita // Prepared\n  expediata // Shipped\n  livrata // Delivered\n  completa // Complete\n  anulata // Cancelled\n  stornata // Voided\n  returnata // Returned\n  partiala // Partially fulfilled\n}\n\nenum ShippingMethod {\n  curier\n  showroom\n}\n\nenum ShipmentStatus {\n  asteptare // Waiting\n  prelucrare // Processing\n  pregatit // Ready\n  expediat // Shipped\n  tranzit // In transit\n  livrat // Delivered\n  esuat // Failed\n  intors // Returned\n  anulat // Cancelled\n  partial // Partially shipped\n}\n\nenum PaymentMethod {\n  ramburs\n  card\n  transfer\n  laTermen\n}\n\nenum PaymentStatus {\n  asteptare\n  succes\n  esuat\n  rambursat\n  partial_rambursat\n  contestat\n}\n\nenum StockStatus {\n  IN_STOCK\n  LOW_STOCK\n  OUT_OF_STOCK\n  DISCONTINUED\n  UNKNOWN\n}\n\nmodel OrderStatusHistory {\n  id      String @id @default(cuid())\n  orderId String\n  order   Order  @relation(fields: [orderId], references: [id], onDelete: Cascade)\n\n  // Status changes\n  orderStatus    OrderStatus?\n  paymentStatus  PaymentStatus?\n  shipmentStatus ShipmentStatus?\n\n  // Previous values\n  previousOrderStatus    OrderStatus?\n  previousPaymentStatus  PaymentStatus?\n  previousShipmentStatus ShipmentStatus?\n\n  // Metadata\n  reason    String?\n  notes     String?\n  changedBy String?\n  ipAddress String?\n  userAgent String?\n\n  createdAt DateTime @default(now())\n\n  @@index([orderId])\n  @@index([orderId, createdAt])\n  @@index([orderStatus])\n  @@index([shipmentStatus])\n  @@index([paymentStatus])\n  @@index([changedBy])\n}\n\nmodel Return {\n  id           String @id @default(cuid())\n  returnNumber String @unique // Human-readable return number (e.g., RET-2023-00001)\n\n  // Related order\n  orderId String\n  order   Order  @relation(fields: [orderId], references: [id], onDelete: Restrict)\n\n  // Status tracking\n  status          ReturnStatus @default(requested)\n  reason          ReturnReason\n  additionalNotes String?      @db.Text\n\n  // Approval details\n  isApproved      Boolean? // null = pending, true/false = decision made\n  approvedBy      String?\n  approvedAt      DateTime?\n  rejectionReason String?   @db.Text\n\n  // Financial details\n  refundAmount    Decimal?      @db.Decimal(15, 2)\n  refundMethod    RefundMethod?\n  refundedAt      DateTime?\n  refundReference String?\n\n  // Logistics\n  returnShippingLabel String? // URL or reference to shipping label\n  receivedAt          DateTime? // When items were received back\n  inspectedAt         DateTime? // When items were inspected\n\n  // Items being returned\n  returnItems   ReturnItem[]\n  statusHistory ReturnStatusHistory[]\n\n  // Audit fields\n  createdBy String\n  updatedBy String?\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n\n  @@index([orderId])\n  @@index([status])\n  @@index([createdAt])\n  @@index([returnNumber])\n}\n\nmodel ReturnItem {\n  id String @id @default(cuid())\n\n  // Related return and original order item\n  returnId    String\n  return      Return    @relation(fields: [returnId], references: [id], onDelete: Cascade)\n  orderItemId String\n  orderItem   OrderItem @relation(fields: [orderItemId], references: [id], onDelete: Restrict)\n\n  // Return details\n  quantity    Int\n  reason      ReturnItemReason\n  condition   ItemCondition    @default(asDescribed)\n  description String?          @db.Text\n\n  // Processing details\n  isReceived       Boolean           @default(false)\n  isInspected      Boolean           @default(false)\n  inspectionNotes  String?           @db.Text\n  inspectionResult InspectionResult?\n\n  // Audit fields\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n\n  @@unique([returnId, orderItemId]) // Prevent duplicate items in a return\n  @@index([returnId])\n  @@index([orderItemId])\n}\n\nmodel ReturnStatusHistory {\n  id       String @id @default(cuid())\n  returnId String\n  return   Return @relation(fields: [returnId], references: [id], onDelete: Cascade)\n\n  previousStatus ReturnStatus?\n  newStatus      ReturnStatus\n\n  notes     String? @db.Text\n  changedBy String\n\n  createdAt DateTime @default(now())\n\n  @@index([returnId])\n  @@index([returnId, createdAt])\n}\n\nmodel ServiceRequest {\n  id            String @id @default(cuid())\n  serviceNumber String @unique // Human-readable service number (e.g., SRV-2023-00001)\n\n  // Customer and vehicle info\n  userId       String\n  user         User    @relation(fields: [userId], references: [id], onDelete: Restrict)\n  vin          String? // Vehicle identification number\n  vehicleModel String?\n  vehicleYear  Int?\n  mileage      Int?\n\n  // Service details\n  type           ServiceType   @default(repair)\n  status         ServiceStatus @default(requested)\n  description    String        @db.Text\n  diagnosisNotes String?       @db.Text\n\n  // Scheduling\n  preferredDate     DateTime?\n  scheduledDate     DateTime?\n  completedDate     DateTime?\n  estimatedDuration Int? // In minutes\n\n  // Financial\n  estimatedCost Decimal?  @db.Decimal(15, 2)\n  finalCost     Decimal?  @db.Decimal(15, 2)\n  isPaid        Boolean   @default(false)\n  paidAt        DateTime?\n\n  // Related items\n  serviceItems  ServiceItem[]\n  statusHistory ServiceStatusHistory[]\n\n  // Audit fields\n  createdBy String\n  updatedBy String?\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n  Order     Order[]\n\n  @@index([userId])\n  @@index([status])\n  @@index([serviceNumber])\n  @@index([scheduledDate])\n  @@index([vin])\n}\n\nmodel ServiceItem {\n  id String @id @default(cuid())\n\n  serviceRequestId String\n  serviceRequest   ServiceRequest @relation(fields: [serviceRequestId], references: [id], onDelete: Cascade)\n\n  // Can be linked to a product or just described\n  productId String?\n  product   Product? @relation(fields: [productId], references: [id], onDelete: SetNull)\n\n  description String\n  quantity    Int      @default(1)\n  unitPrice   Decimal? @db.Decimal(15, 2)\n  totalPrice  Decimal? @db.Decimal(15, 2)\n\n  // Labor vs Parts\n  itemType   ServiceItemType @default(part)\n  laborHours Decimal?        @db.Decimal(5, 2)\n\n  // Audit fields\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n\n  @@index([serviceRequestId])\n  @@index([productId])\n}\n\nmodel ServiceStatusHistory {\n  id               String         @id @default(cuid())\n  serviceRequestId String\n  serviceRequest   ServiceRequest @relation(fields: [serviceRequestId], references: [id], onDelete: Cascade)\n\n  previousStatus ServiceStatus?\n  newStatus      ServiceStatus\n\n  notes     String? @db.Text\n  changedBy String\n\n  createdAt DateTime @default(now())\n\n  @@index([serviceRequestId])\n  @@index([serviceRequestId, createdAt])\n}\n\nmodel CategorySection {\n  id          String @id @default(cuid())\n  name        String\n  description String\n  image       String\n  href        String\n}\n\n// Return-related enums\nenum ReturnStatus {\n  requested\n  approved\n  rejected\n  awaitingReceipt\n  received\n  inspected\n  refundIssued\n  completed\n  cancelled\n}\n\nenum ReturnReason {\n  wrongItem\n  defective\n  damaged\n  notAsDescribed\n  noLongerWanted\n  other\n}\n\nenum ReturnItemReason {\n  wrongItem\n  defective\n  damaged\n  notAsDescribed\n  noLongerWanted\n  other\n}\n\nenum ItemCondition {\n  asDescribed\n  damaged\n  opened\n  used\n  missingParts\n}\n\nenum InspectionResult {\n  approved\n  rejected\n  partiallyApproved\n}\n\nenum RefundMethod {\n  originalPayment\n  storeCredit\n  bankTransfer\n}\n\n// Service-related enums\nenum ServiceType {\n  repair\n  maintenance\n  warranty\n  installation\n  inspection\n  other\n}\n\nenum ServiceStatus {\n  requested\n  scheduled\n  inProgress\n  diagnosisComplete\n  awaitingParts\n  awaitingApproval\n  completed\n  cancelled\n  delivered\n}\n\nenum ServiceItemType {\n  part\n  labor\n  fee\n  other\n}\n\n//triggers that i use:\n\n/**\n * CREATE OR REPLACE FUNCTION log_product_history()\n * RETURNS TRIGGER AS $$\n * DECLARE\n * new_version INT;\n * changes_json jsonb := '{}'::jsonb;\n * field_name TEXT;\n * old_value jsonb;\n * new_value jsonb;\n * BEGIN\n * -- Determine the version number\n * SELECT COALESCE(MAX(version), 0) + 1 INTO new_version\n * FROM \"ProductHistory\"\n * WHERE \"Material_Number\" = NEW.\"Material_Number\";\n * -- Handle INSERT operation\n * IF (TG_OP = 'INSERT') THEN\n * INSERT INTO \"ProductHistory\" (\n * \"id\", \"Material_Number\", \"changes\", \"updatedAt\", \"snapshot\", \"change_type\", \"version\", \"changed_by\"\n * )\n * VALUES\n * (\n * gen_random_uuid(),\n * NEW.\"Material_Number\",\n * '{}'::jsonb,   -- No changes in the insert case\n * NOW(),\n * to_jsonb(NEW),\n * 'INSERT',\n * new_version,\n * 'T-GIS'\n * );\n * -- Handle UPDATE operation\n * ELSIF (TG_OP = 'UPDATE') THEN\n * -- Iterate through each field to detect changes\n * FOR field_name, old_value IN\n * SELECT key, value\n * FROM jsonb_each(to_jsonb(OLD))\n * LOOP\n * -- *** Add this condition to skip the specific field ***\n * IF field_name = 'last_updated_at' THEN\n * CONTINUE; -- Skips the rest of the loop body for this iteration\n * END IF;\n * SELECT value INTO new_value\n * FROM jsonb_each(to_jsonb(NEW))\n * WHERE key = field_name;\n * -- Check if the value has changed\n * IF old_value IS DISTINCT FROM new_value THEN\n * changes_json := jsonb_set(changes_json, ARRAY[field_name], new_value);\n * END IF;\n * END LOOP;\n * -- Only insert a record if there are actual changes\n * IF changes_json <> '{}'::jsonb THEN\n * INSERT INTO \"ProductHistory\" (\n * \"id\", \"Material_Number\", \"changes\", \"updatedAt\", \"snapshot\", \"change_type\", \"version\", \"changed_by\"\n * )\n * VALUES\n * (\n * gen_random_uuid(),\n * NEW.\"Material_Number\",\n * changes_json,\n * NOW(),\n * to_jsonb(NEW),\n * 'UPDATE',\n * new_version,\n * 'T-GIS'\n * );\n * END IF;\n * END IF;\n * RETURN NEW;\n * END;\n * $$ LANGUAGE plpgsql;\n * DROP TRIGGER IF EXISTS product_history_trigger ON \"Product\";\n * CREATE TRIGGER product_history_trigger\n * AFTER INSERT OR UPDATE ON \"Product\"\n * FOR EACH ROW\n * EXECUTE FUNCTION log_product_history();\n * --------------------------------------------\n * -- Create a trigger function to log price changes\n * CREATE OR REPLACE FUNCTION log_price_history()\n * RETURNS TRIGGER AS $$\n * BEGIN\n * -- Only create a history record if price actually changed\n * IF (OLD.\"PretAM\" IS DISTINCT FROM NEW.\"PretAM\" OR\n * OLD.\"FinalPrice\" IS DISTINCT FROM NEW.\"FinalPrice\") THEN\n * INSERT INTO \"PriceHistory\" (\n * \"id\",\n * \"productId\",\n * \"oldPretAM\",\n * \"newPretAM\",\n * \"oldFinalPrice\",\n * \"newFinalPrice\",\n * \"reason\",\n * \"source\",\n * \"createdBy\",\n * \"createdAt\"\n * )\n * VALUES (\n * gen_random_uuid(),\n * NEW.\"id\",\n * OLD.\"PretAM\",\n * NEW.\"PretAM\",\n * OLD.\"FinalPrice\",\n * NEW.\"FinalPrice\",\n * CASE\n * WHEN OLD.\"PretAM\" IS DISTINCT FROM NEW.\"PretAM\" THEN 'Base price update'\n * ELSE 'Discount change'\n * END,\n * 'System trigger',\n * 'system',\n * NOW()\n * );\n * END IF;\n * RETURN NEW;\n * END;\n * $$ LANGUAGE plpgsql;\n * -- Create the trigger\n * DROP TRIGGER IF EXISTS product_price_history_trigger ON \"Product\";\n * CREATE TRIGGER product_price_history_trigger\n * AFTER UPDATE OF \"PretAM\", \"FinalPrice\" ON \"Product\"\n * FOR EACH ROW\n * EXECUTE FUNCTION log_price_history();\n * --------------------------------------------------------------\n * -- Create a function to calculate final price based on discount\n * CREATE OR REPLACE FUNCTION calculate_final_price()\n * RETURNS TRIGGER AS $$\n * DECLARE\n * v_discount_type TEXT;\n * v_discount_value DECIMAL;\n * v_has_discount BOOLEAN := FALSE;\n * v_discount_percentage DECIMAL := NULL;\n * BEGIN\n * -- Check if product has a discount\n * SELECT d.\"type\", d.\"value\" INTO v_discount_type, v_discount_value\n * FROM \"ProductDiscount\" pd\n * JOIN \"Discount\" d ON pd.\"discountId\" = d.\"id\"\n * WHERE pd.\"productId\" = NEW.\"id\" AND d.\"active\" = TRUE\n * AND d.\"startDate\" <= CURRENT_TIMESTAMP\n * AND d.\"endDate\" >= CURRENT_TIMESTAMP\n * LIMIT 1;\n * -- If no active discount found, final price equals base price\n * IF v_discount_type IS NULL THEN\n * NEW.\"FinalPrice\" := NEW.\"PretAM\";\n * NEW.\"HasDiscount\" := FALSE;\n * NEW.\"discountPercentage\" := NULL;\n * ELSE\n * -- Calculate final price based on discount type\n * v_has_discount := TRUE;\n * CASE v_discount_type\n * WHEN 'PERCENTAGE' THEN\n * -- Ensure percentage is within valid range (0-100)\n * IF v_discount_value > 0 AND v_discount_value <= 100 THEN\n * NEW.\"FinalPrice\" := NEW.\"PretAM\" - (NEW.\"PretAM\" * v_discount_value / 100);\n * v_discount_percentage := v_discount_value;\n * ELSE\n * NEW.\"FinalPrice\" := NEW.\"PretAM\";\n * v_has_discount := FALSE;\n * END IF;\n * WHEN 'FIXED_AMOUNT' THEN\n * -- Ensure fixed amount doesn't make price negative\n * IF v_discount_value < NEW.\"PretAM\" THEN\n * NEW.\"FinalPrice\" := NEW.\"PretAM\" - v_discount_value;\n * -- Calculate equivalent percentage for display\n * v_discount_percentage := (v_discount_value / NEW.\"PretAM\") * 100;\n * ELSE\n * NEW.\"FinalPrice\" := 0;\n * v_discount_percentage := 100;\n * END IF;\n * WHEN 'NEW_PRICE' THEN\n * -- New price becomes the final price\n * IF v_discount_value < NEW.\"PretAM\" THEN\n * NEW.\"FinalPrice\" := v_discount_value;\n * -- Calculate equivalent percentage for display\n * v_discount_percentage := ((NEW.\"PretAM\" - v_discount_value) / NEW.\"PretAM\") * 100;\n * ELSE\n * -- If new price is higher than base price, don't apply discount\n * NEW.\"FinalPrice\" := NEW.\"PretAM\";\n * v_has_discount := FALSE;\n * END IF;\n * ELSE\n * -- Unknown discount type, use base price\n * NEW.\"FinalPrice\" := NEW.\"PretAM\";\n * v_has_discount := FALSE;\n * END CASE;\n * NEW.\"HasDiscount\" := v_has_discount;\n * NEW.\"discountPercentage\" := v_discount_percentage;\n * END IF;\n * RETURN NEW;\n * END;\n * $$ LANGUAGE plpgsql;\n * -- Create trigger to recalculate final price when base price changes\n * DROP TRIGGER IF EXISTS update_product_price ON \"Product\";\n * CREATE TRIGGER update_product_price\n * BEFORE INSERT OR UPDATE OF \"PretAM\" ON \"Product\"\n * FOR EACH ROW\n * EXECUTE FUNCTION calculate_final_price();\n * -- Create trigger to recalculate prices when discounts are added/removed\n * CREATE OR REPLACE FUNCTION update_product_price_on_discount_change()\n * RETURNS TRIGGER AS $$\n * BEGIN\n * -- Update the product to trigger the calculate_final_price function\n * IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN\n * UPDATE \"Product\" SET \"id\" = \"id\" WHERE \"id\" = NEW.\"productId\";\n * ELSIF TG_OP = 'DELETE' THEN\n * UPDATE \"Product\" SET \"id\" = \"id\" WHERE \"id\" = OLD.\"productId\";\n * END IF;\n * RETURN NULL;\n * END;\n * $$ LANGUAGE plpgsql;\n * -- Create triggers for discount changes\n * DROP TRIGGER IF EXISTS product_discount_insert_trigger ON \"ProductDiscount\";\n * DROP TRIGGER IF EXISTS product_discount_update_trigger ON \"ProductDiscount\";\n * DROP TRIGGER IF EXISTS product_discount_delete_trigger ON \"ProductDiscount\";\n * CREATE TRIGGER product_discount_insert_trigger\n * AFTER INSERT ON \"ProductDiscount\"\n * FOR EACH ROW\n * EXECUTE FUNCTION update_product_price_on_discount_change();\n * CREATE TRIGGER product_discount_update_trigger\n * AFTER UPDATE ON \"ProductDiscount\"\n * FOR EACH ROW\n * EXECUTE FUNCTION update_product_price_on_discount_change();\n * CREATE TRIGGER product_discount_delete_trigger\n * AFTER DELETE ON \"ProductDiscount\"\n * FOR EACH ROW\n * EXECUTE FUNCTION update_product_price_on_discount_change();\n * -- Also update prices when discount details change\n * CREATE OR REPLACE FUNCTION update_products_on_discount_change()\n * RETURNS TRIGGER AS $$\n * BEGIN\n * -- Update all products using this discount to recalculate prices\n * IF TG_OP = 'UPDATE' AND (\n * OLD.\"value\" IS DISTINCT FROM NEW.\"value\" OR\n * OLD.\"type\" IS DISTINCT FROM NEW.\"type\" OR\n * OLD.\"active\" IS DISTINCT FROM NEW.\"active\" OR\n * OLD.\"startDate\" IS DISTINCT FROM NEW.\"startDate\" OR\n * OLD.\"endDate\" IS DISTINCT FROM NEW.\"endDate\"\n * ) THEN\n * UPDATE \"Product\" p\n * SET \"id\" = p.\"id\"\n * FROM \"ProductDiscount\" pd\n * WHERE pd.\"productId\" = p.\"id\" AND pd.\"discountId\" = NEW.\"id\";\n * END IF;\n * RETURN NULL;\n * END;\n * $$ LANGUAGE plpgsql;\n * CREATE TRIGGER discount_update_trigger\n * AFTER UPDATE ON \"Discount\"\n * FOR EACH ROW\n * EXECUTE FUNCTION update_products_on_discount_change();\n * ------------------------------------------------------\n * for Bulk Discount Support\n * -- Modify the calculate_final_price function to check for bulk discounts - a familiyCode\n * CREATE OR REPLACE FUNCTION calculate_final_price()\n * RETURNS TRIGGER AS $$\n * DECLARE\n * v_discount_type TEXT;\n * v_discount_value DECIMAL;\n * v_has_discount BOOLEAN := FALSE;\n * v_discount_percentage DECIMAL := NULL;\n * v_discount_record RECORD;\n * BEGIN\n * -- First check for direct product discount\n * SELECT d.\"type\", d.\"value\" INTO v_discount_type, v_discount_value\n * FROM \"ProductDiscount\" pd\n * JOIN \"Discount\" d ON pd.\"discountId\" = d.\"id\"\n * WHERE pd.\"productId\" = NEW.\"id\" AND d.\"active\" = TRUE\n * AND d.\"startDate\" <= CURRENT_TIMESTAMP\n * AND d.\"endDate\" >= CURRENT_TIMESTAMP\n * LIMIT 1;\n * -- If no direct discount, check for category/brand/class discounts\n * IF v_discount_type IS NULL THEN\n * SELECT d.* INTO v_discount_record\n * FROM \"Discount\" d\n * WHERE d.\"active\" = TRUE\n * AND d.\"startDate\" <= CURRENT_TIMESTAMP\n * AND d.\"endDate\" >= CURRENT_TIMESTAMP\n * AND (\n * (d.\"applyToCategory\" IS NOT NULL AND d.\"applyToCategory\" = NEW.\"Material_Group\") OR\n * (d.\"applyToBrand\" IS NOT NULL AND EXISTS (\n * SELECT 1 FROM \"ProductClass\" pc\n * WHERE pc.\"id\" = NEW.\"classId\"\n * AND pc.\"brandId\" = d.\"applyToBrand\"\n * )) OR\n * (d.\"applyToClass\" IS NOT NULL AND d.\"applyToClass\" = NEW.\"Parts_Class\")\n * )\n * ORDER BY d.\"priority\" DESC\n * LIMIT 1;\n * IF v_discount_record IS NOT NULL THEN\n * v_discount_type := v_discount_record.\"type\";\n * v_discount_value := v_discount_record.\"value\";\n * END IF;\n * END IF;\n * -- Rest of the function remains the same...\n * -- (calculation logic based on discount type)\n * RETURN NEW;\n * END;\n * $$ LANGUAGE plpgsql;\n * ***However, for 400K products, this approach might cause performance issues when updating discounts.\n * ****A more efficient approach would be to pre-calculate and materialize the discounts during off-peak hours.\n */\n\n/**\n * -- Drop existing function to replace it\n * DROP FUNCTION IF EXISTS calculate_final_price();\n * -- Create improved function to calculate final price based on discount\n * CREATE OR REPLACE FUNCTION calculate_final_price()\n * RETURNS TRIGGER AS $$\n * DECLARE\n * v_discount_type TEXT;\n * v_discount_value DECIMAL;\n * v_has_discount BOOLEAN := FALSE;\n * v_discount_percentage DECIMAL := NULL;\n * v_base_price DECIMAL;\n * BEGIN\n * -- Ensure we have a base price to work with\n * IF NEW.\"PretAM\" IS NULL THEN\n * RETURN NEW;\n * END IF;\n * v_base_price := NEW.\"PretAM\";\n * -- Check if product has a discount\n * SELECT d.\"type\", d.\"value\" INTO v_discount_type, v_discount_value\n * FROM \"ProductDiscount\" pd\n * JOIN \"Discount\" d ON pd.\"discountId\" = d.\"id\"\n * WHERE pd.\"productId\" = NEW.\"id\" AND d.\"active\" = TRUE\n * AND d.\"startDate\" <= CURRENT_TIMESTAMP\n * AND d.\"endDate\" >= CURRENT_TIMESTAMP\n * LIMIT 1;\n * -- Debug logging (remove in production)\n * RAISE NOTICE 'Product ID: %, Discount Type: %, Discount Value: %',\n * NEW.\"id\", v_discount_type, v_discount_value;\n * -- If no active discount found, final price equals base price\n * IF v_discount_type IS NULL THEN\n * NEW.\"FinalPrice\" := v_base_price;\n * NEW.\"HasDiscount\" := FALSE;\n * NEW.\"discountPercentage\" := NULL;\n * RAISE NOTICE 'No discount found, setting FinalPrice to %', v_base_price;\n * ELSE\n * -- Calculate final price based on discount type\n * v_has_discount := TRUE;\n * CASE v_discount_type\n * WHEN 'PERCENTAGE' THEN\n * -- Ensure percentage is within valid range (0-100)\n * IF v_discount_value > 0 AND v_discount_value <= 100 THEN\n * NEW.\"FinalPrice\" := v_base_price - (v_base_price * v_discount_value / 100);\n * v_discount_percentage := v_discount_value;\n * RAISE NOTICE 'PERCENTAGE discount: % -> %', v_discount_value, NEW.\"FinalPrice\";\n * ELSE\n * NEW.\"FinalPrice\" := v_base_price;\n * v_has_discount := FALSE;\n * RAISE NOTICE 'Invalid percentage: %', v_discount_value;\n * END IF;\n * WHEN 'FIXED_AMOUNT' THEN\n * -- Apply fixed amount discount\n * NEW.\"FinalPrice\" := GREATEST(v_base_price - v_discount_value, 0);\n * -- Calculate equivalent percentage for display\n * IF v_base_price > 0 THEN\n * v_discount_percentage := LEAST((v_discount_value / v_base_price) * 100, 100);\n * ELSE\n * v_discount_percentage := 0;\n * END IF;\n * RAISE NOTICE 'FIXED_AMOUNT discount: % -> %', v_discount_value, NEW.\"FinalPrice\";\n * WHEN 'NEW_PRICE' THEN\n * -- New price becomes the final price if it's lower than base price\n * IF v_discount_value < v_base_price THEN\n * NEW.\"FinalPrice\" := v_discount_value;\n * -- Calculate equivalent percentage for display\n * IF v_base_price > 0 THEN\n * v_discount_percentage := ((v_base_price - v_discount_value) / v_base_price) * 100;\n * ELSE\n * v_discount_percentage := 0;\n * END IF;\n * RAISE NOTICE 'NEW_PRICE discount: % -> %', v_discount_value, NEW.\"FinalPrice\";\n * ELSE\n * -- If new price is higher than base price, don't apply discount\n * NEW.\"FinalPrice\" := v_base_price;\n * v_has_discount := FALSE;\n * RAISE NOTICE 'NEW_PRICE higher than base price: % > %', v_discount_value, v_base_price;\n * END IF;\n * ELSE\n * -- Unknown discount type, use base price\n * NEW.\"FinalPrice\" := v_base_price;\n * v_has_discount := FALSE;\n * RAISE NOTICE 'Unknown discount type: %', v_discount_type;\n * END CASE;\n * NEW.\"HasDiscount\" := v_has_discount;\n * NEW.\"discountPercentage\" := v_discount_percentage;\n * END IF;\n * -- Ensure FinalPrice is never NULL\n * IF NEW.\"FinalPrice\" IS NULL THEN\n * NEW.\"FinalPrice\" := v_base_price;\n * RAISE NOTICE 'FinalPrice was NULL, setting to base price: %', v_base_price;\n * END IF;\n * RETURN NEW;\n * END;\n * $$ LANGUAGE plpgsql;\n * -- Recreate the trigger\n * DROP TRIGGER IF EXISTS update_product_price ON \"Product\";\n * CREATE TRIGGER update_product_price\n * BEFORE INSERT OR UPDATE ON \"Product\"\n * FOR EACH ROW\n * EXECUTE FUNCTION calculate_final_price();\n * -- Also ensure the discount change trigger works for all discount types\n * CREATE OR REPLACE FUNCTION update_product_price_on_discount_change()\n * RETURNS TRIGGER AS $$\n * BEGIN\n * -- Get the product ID\n * DECLARE\n * v_product_id TEXT;\n * BEGIN\n * IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN\n * v_product_id := NEW.\"productId\";\n * ELSIF TG_OP = 'DELETE' THEN\n * v_product_id := OLD.\"productId\";\n * END IF;\n * -- Force recalculation by updating the product\n * IF v_product_id IS NOT NULL THEN\n * RAISE NOTICE 'Updating product % due to discount change', v_product_id;\n * UPDATE \"Product\"\n * SET \"id\" = \"id\" -- This is a no-op update that triggers the calculate_final_price function\n * WHERE \"id\" = v_product_id;\n * END IF;\n * END;\n * RETURN NULL;\n * END;\n * $$ LANGUAGE plpgsql;\n * -- Recreate the discount change triggers\n * DROP TRIGGER IF EXISTS product_discount_insert_trigger ON \"ProductDiscount\";\n * DROP TRIGGER IF EXISTS product_discount_update_trigger ON \"ProductDiscount\";\n * DROP TRIGGER IF EXISTS product_discount_delete_trigger ON \"ProductDiscount\";\n * CREATE TRIGGER product_discount_insert_trigger\n * AFTER INSERT ON \"ProductDiscount\"\n * FOR EACH ROW\n * EXECUTE FUNCTION update_product_price_on_discount_change();\n * CREATE TRIGGER product_discount_update_trigger\n * AFTER UPDATE ON \"ProductDiscount\"\n * FOR EACH ROW\n * EXECUTE FUNCTION update_product_price_on_discount_change();\n * CREATE TRIGGER product_discount_delete_trigger\n * AFTER DELETE ON \"ProductDiscount\"\n * FOR EACH ROW\n * EXECUTE FUNCTION update_product_price_on_discount_change();\n * -- Also update the discount update trigger\n * CREATE OR REPLACE FUNCTION update_products_on_discount_change()\n * RETURNS TRIGGER AS $$\n * BEGIN\n * -- Update all products using this discount to recalculate prices\n * IF TG_OP = 'UPDATE' AND (\n * OLD.\"value\" IS DISTINCT FROM NEW.\"value\" OR\n * OLD.\"type\" IS DISTINCT FROM NEW.\"type\" OR\n * OLD.\"active\" IS DISTINCT FROM NEW.\"active\" OR\n * OLD.\"startDate\" IS DISTINCT FROM NEW.\"startDate\" OR\n * OLD.\"endDate\" IS DISTINCT FROM NEW.\"endDate\"\n * ) THEN\n * RAISE NOTICE 'Discount % changed, updating all associated products', NEW.\"id\";\n * UPDATE \"Product\" p\n * SET \"id\" = p.\"id\" -- This is a no-op update that triggers the calculate_final_price function\n * FROM \"ProductDiscount\" pd\n * WHERE pd.\"productId\" = p.\"id\" AND pd.\"discountId\" = NEW.\"id\";\n * END IF;\n * RETURN NULL;\n * END;\n * $$ LANGUAGE plpgsql;\n * -- Recreate the discount update trigger\n * DROP TRIGGER IF EXISTS discount_update_trigger ON \"Discount\";\n * CREATE TRIGGER discount_update_trigger\n * AFTER UPDATE ON \"Discount\"\n * FOR EACH ROW\n * EXECUTE FUNCTION update_products_on_discount_change();\n */\n",
  "inlineSchemaHash": "a16bb39abc8193b174349db5d3f6e1ea7d57ae8a385c1d9276c4953e318e693c",
  "copyEngine": true
}

const fs = require('fs')

config.dirname = __dirname
if (!fs.existsSync(path.join(__dirname, 'schema.prisma'))) {
  const alternativePaths = [
    "generated/prisma",
    "prisma",
  ]
  
  const alternativePath = alternativePaths.find((altPath) => {
    return fs.existsSync(path.join(process.cwd(), altPath, 'schema.prisma'))
  }) ?? alternativePaths[0]

  config.dirname = path.join(process.cwd(), alternativePath)
  config.isBundled = true
}

config.runtimeDataModel = JSON.parse("{\"models\":{\"User\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"email\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"emailVerified\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"firstName\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"lastName\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"profileImage\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userAM\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"phoneNumber\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"newsletterOptIn\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"externalId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"externalProvider\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"salutation\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"MisterMiss\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"role\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Rol\",\"nativeType\":null,\"default\":\"inregistratAB\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"jobTitle\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"department\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"bio\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Text\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"preferredLanguage\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"timezone\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"permissions\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"accessGroups\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserGroup\",\"nativeType\":null,\"relationName\":\"UserToGroup\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"lastLoginAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"loginCount\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"nativeType\":null,\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"lastActivityAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"isActive\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"inactiveBy\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"inactiveAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"inactiveReason\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Text\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"isSuspended\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"suspendedBy\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"suspendedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"suspensionReason\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Text\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"deletedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"deletedBy\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"deletedReason\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Text\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"passwordEnabled\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"twoFactorEnabled\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"totpEnabled\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"mfaEnabledAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"mfaDisabledAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"loginAttempts\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"nativeType\":null,\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"lockoutUntil\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"emailNotifications\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"pushNotifications\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"smsNotifications\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"orders\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Order\",\"nativeType\":null,\"relationName\":\"OrderToUser\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"wishlist\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Wishlist\",\"nativeType\":null,\"relationName\":\"UserToWishlist\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"shippingAddresses\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ShippingAddress\",\"nativeType\":null,\"relationName\":\"ShippingAddressToUser\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"billingAddresses\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"BillingAddress\",\"nativeType\":null,\"relationName\":\"BillingAddressToUser\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"sessions\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserSession\",\"nativeType\":null,\"relationName\":\"UserToUserSession\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"serviceRequests\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ServiceRequest\",\"nativeType\":null,\"relationName\":\"ServiceRequestToUser\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"notifications\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserNotification\",\"nativeType\":null,\"relationName\":\"UserToUserNotification\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"auditLogs\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UserAuditLog\",\"nativeType\":null,\"relationName\":\"UserToUserAuditLog\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"createdBy\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedBy\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"UserSession\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"UserToUserSession\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"sessionToken\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"expiresAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"ipAddress\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userAgent\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"deviceId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"location\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"lastActiveAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"isRevoked\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"revokedReason\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"UserGroup\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"description\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Text\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"permissions\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"users\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"UserToGroup\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"createdBy\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedBy\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"UserNotification\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"UserToUserNotification\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"title\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"message\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Text\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"type\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"NotificationType\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"isRead\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"readAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"link\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"UserAuditLog\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"UserToUserAuditLog\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"SetNull\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"action\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityType\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"entityId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"details\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Text\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"ipAddress\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userAgent\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"performedBy\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"Order\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"orderNumber\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"amount\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"nativeType\":[\"Decimal\",[\"15\",\"2\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"isPaid\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"vin\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"invoiceAM\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatesEnabled\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"terms\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"orderStatus\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"OrderStatus\",\"nativeType\":null,\"default\":\"plasata\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"paymentStatus\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"PaymentStatus\",\"nativeType\":null,\"default\":\"asteptare\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"paymentMethod\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"PaymentMethod\",\"nativeType\":null,\"default\":\"ramburs\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"shippingMethod\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"ShippingMethod\",\"nativeType\":null,\"default\":\"curier\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"shipmentStatus\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"ShipmentStatus\",\"nativeType\":null,\"default\":\"asteptare\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"showroom\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Showroom\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"placedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"processedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"completedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"cancelledAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"shippingProcessedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"shippedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"deliveredAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"paidAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"refundedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdBy\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedBy\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"version\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"nativeType\":null,\"default\":1,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"isActive\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"deletedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"archivedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"orderItems\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"OrderItem\",\"nativeType\":null,\"relationName\":\"OrderToOrderItem\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"statusHistory\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"OrderStatusHistory\",\"nativeType\":null,\"relationName\":\"OrderToOrderStatusHistory\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"notes\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"billingAddressId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"billingAddress\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"BillingAddress\",\"nativeType\":null,\"relationName\":\"BillingAddressToOrder\",\"relationFromFields\":[\"billingAddressId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Restrict\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"shippingAddressId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"shippingAddress\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ShippingAddress\",\"nativeType\":null,\"relationName\":\"OrderToShippingAddress\",\"relationFromFields\":[\"shippingAddressId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Restrict\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"OrderToUser\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Restrict\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"returns\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Return\",\"nativeType\":null,\"relationName\":\"OrderToReturn\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"serviceRequests\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ServiceRequest\",\"nativeType\":null,\"relationName\":\"OrderToServiceRequest\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"hasReturns\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"hasServiceRequests\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"OrderItem\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"quantity\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"price\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"nativeType\":[\"Decimal\",[\"10\",\"2\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"notes\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"notesToInvoice\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"vinOrderItem\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdBy\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedBy\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"version\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"nativeType\":null,\"default\":1,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"orderId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"order\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Order\",\"nativeType\":null,\"relationName\":\"OrderToOrderItem\",\"relationFromFields\":[\"orderId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"productId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"product\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Product\",\"nativeType\":null,\"relationName\":\"OrderItemToProduct\",\"relationFromFields\":[\"productId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Restrict\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"ReturnItem\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ReturnItem\",\"nativeType\":null,\"relationName\":\"OrderItemToReturnItem\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[[\"orderId\",\"productId\"]],\"uniqueIndexes\":[{\"name\":null,\"fields\":[\"orderId\",\"productId\"]}],\"isGenerated\":false},\"Wishlist\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"productCode\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"product\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Product\",\"nativeType\":null,\"relationName\":\"ProductToWishlist\",\"relationFromFields\":[\"productCode\"],\"relationToFields\":[\"Material_Number\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"UserToWishlist\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true}],\"primaryKey\":null,\"uniqueFields\":[[\"userId\",\"productCode\"]],\"uniqueIndexes\":[{\"name\":null,\"fields\":[\"userId\",\"productCode\"]}],\"isGenerated\":false},\"ShippingAddress\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"fullName\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"address\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"city\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"county\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"phoneNumber\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"notes\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"isDefault\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"orders\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Order\",\"nativeType\":null,\"relationName\":\"OrderToShippingAddress\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"ShippingAddressToUser\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"BillingAddress\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"fullName\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"companyName\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"address\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"city\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"county\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"cui\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"bank\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"iban\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"isDefault\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"orders\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Order\",\"nativeType\":null,\"relationName\":\"BillingAddressToOrder\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"BillingAddressToUser\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"Banner\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"title\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"subtitle\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"imageUrl\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"mobileImageUrl\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"callToAction\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"buttonText\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"description\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"url\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"placement\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"BannerPlacement\",\"nativeType\":null,\"default\":\"HOME\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"position\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"nativeType\":null,\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"width\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"height\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"backgroundColor\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"textColor\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"textAlignment\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"targetAudience\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"deviceTarget\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DeviceTarget\",\"nativeType\":null,\"default\":\"ALL\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"startDate\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"endDate\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"isActive\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"impressions\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"nativeType\":null,\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"clicks\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"nativeType\":null,\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"conversionRate\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Float\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdBy\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedBy\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"CategoryLevel1\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"nameRO\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"afisat\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"imageUrl\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"level2Categories\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"CategoryLevel2\",\"nativeType\":null,\"relationName\":\"CategoryLevel1ToCategoryLevel2\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"CategoryLevel2\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"nameRO\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"afisat\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"imageUrl\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"level1Id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"level1\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"CategoryLevel1\",\"nativeType\":null,\"relationName\":\"CategoryLevel1ToCategoryLevel2\",\"relationFromFields\":[\"level1Id\"],\"relationToFields\":[\"id\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"level3Categories\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"CategoryLevel3\",\"nativeType\":null,\"relationName\":\"CategoryLevel2ToCategoryLevel3\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[[\"name\",\"level1Id\"]],\"uniqueIndexes\":[{\"name\":null,\"fields\":[\"name\",\"level1Id\"]}],\"isGenerated\":false},\"CategoryLevel3\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"nameRO\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"descriere\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"afisat\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"familyCode\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"imageUrl\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"slug\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"metaTitle\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"metaDescription\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"displayOrder\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"nativeType\":null,\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"productCount\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"nativeType\":null,\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"lastProductAdded\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"isActive\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"deletedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"level2Id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"level2\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"CategoryLevel2\",\"nativeType\":null,\"relationName\":\"CategoryLevel2ToCategoryLevel3\",\"relationFromFields\":[\"level2Id\"],\"relationToFields\":[\"id\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"products\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Product\",\"nativeType\":null,\"relationName\":\"CategoryLevel3ToProduct\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"Brand\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"nameRO\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"afisat\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"imageUrl\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"productClasses\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ProductClass\",\"nativeType\":null,\"relationName\":\"BrandToProductClass\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"VehicleModel\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"productClasses\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ProductClassVehicleModel\",\"nativeType\":null,\"relationName\":\"ProductClassVehicleModelToVehicleModel\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"ProductClass\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"classCode\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"brandId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"brand\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Brand\",\"nativeType\":null,\"relationName\":\"BrandToProductClass\",\"relationFromFields\":[\"brandId\"],\"relationToFields\":[\"id\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"vehicleModels\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ProductClassVehicleModel\",\"nativeType\":null,\"relationName\":\"ProductClassToProductClassVehicleModel\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"products\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Product\",\"nativeType\":null,\"relationName\":\"ProductToProductClass\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"ProductClassVehicleModel\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"productClassId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"vehicleModelId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"productClass\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ProductClass\",\"nativeType\":null,\"relationName\":\"ProductClassToProductClassVehicleModel\",\"relationFromFields\":[\"productClassId\"],\"relationToFields\":[\"id\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"vehicleModel\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"VehicleModel\",\"nativeType\":null,\"relationName\":\"ProductClassVehicleModelToVehicleModel\",\"relationFromFields\":[\"vehicleModelId\"],\"relationToFields\":[\"id\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":{\"name\":null,\"fields\":[\"productClassId\",\"vehicleModelId\"]},\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"Product\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"Material_Number\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"Net_Weight\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"Description_Local\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"Base_Unit_Of_Measur\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"Cross_Plant\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"New_Material\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"PretAM\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"nativeType\":[\"Decimal\",[\"15\",\"2\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"FinalPrice\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"nativeType\":[\"Decimal\",[\"15\",\"2\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"HasDiscount\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"activeDiscountType\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DiscountType\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"activeDiscountValue\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"nativeType\":[\"Decimal\",[\"10\",\"2\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"discountPercentage\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"nativeType\":[\"Decimal\",[\"5\",\"2\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"priceRange\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"ImageUrl\",\"kind\":\"scalar\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":[\"https://op47vimj99.ufs.sh/f/6Hnm5nafTbm964jRPnfTbm9EeHnDOzysS6K5X27Upql8xtjN\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"IsOnLandingPage\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"Material_Number_normalized\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"Description_Local_normalized\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"stockStatus\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"StockStatus\",\"nativeType\":null,\"default\":\"UNKNOWN\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdBy\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedBy\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"version\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"nativeType\":null,\"default\":1,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"isActive\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"deletedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"attributes\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ProductAttribute\",\"nativeType\":null,\"relationName\":\"ProductToProductAttribute\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"productHistory\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ProductHistory\",\"nativeType\":null,\"relationName\":\"ProductToProductHistory\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"productAttributeHistory\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ProductAttributeHistory\",\"nativeType\":null,\"relationName\":\"ProductToProductAttributeHistory\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"discounts\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ProductDiscount\",\"nativeType\":null,\"relationName\":\"ProductToProductDiscount\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"orderItems\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"OrderItem\",\"nativeType\":null,\"relationName\":\"OrderItemToProduct\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"wishlist\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Wishlist\",\"nativeType\":null,\"relationName\":\"ProductToWishlist\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"PriceHistory\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"PriceHistory\",\"nativeType\":null,\"relationName\":\"PriceHistoryToProduct\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"Parts_Class\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":\"undefined-class\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"classId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"productClass\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ProductClass\",\"nativeType\":null,\"relationName\":\"ProductToProductClass\",\"relationFromFields\":[\"classId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"SetNull\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"Material_Group\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":\"undefined-category\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"categoryLevel3Id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"categoryLevel3\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"CategoryLevel3\",\"nativeType\":null,\"relationName\":\"CategoryLevel3ToProduct\",\"relationFromFields\":[\"categoryLevel3Id\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"SetNull\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"serviceItems\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ServiceItem\",\"nativeType\":null,\"relationName\":\"ProductToServiceItem\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"isServiceable\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"warrantyMonths\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"last_updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"PriceHistory\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"productId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"product\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Product\",\"nativeType\":null,\"relationName\":\"PriceHistoryToProduct\",\"relationFromFields\":[\"productId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"oldPretAM\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"nativeType\":[\"Decimal\",[\"15\",\"2\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"newPretAM\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"nativeType\":[\"Decimal\",[\"15\",\"2\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"oldFinalPrice\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"nativeType\":[\"Decimal\",[\"15\",\"2\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"newFinalPrice\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"nativeType\":[\"Decimal\",[\"15\",\"2\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"reason\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"source\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdBy\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"ProductHistory\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"Uuid\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"Material_Number\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"changes\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"snapshot\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"change_type\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ChangeType\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"version\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"changed_by\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"product\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Product\",\"nativeType\":null,\"relationName\":\"ProductToProductHistory\",\"relationFromFields\":[\"Material_Number\"],\"relationToFields\":[\"Material_Number\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"Discount\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"description\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"type\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DiscountType\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"value\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"nativeType\":[\"Decimal\",[\"10\",\"2\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"startDate\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"endDate\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"active\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdBy\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"productDiscounts\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ProductDiscount\",\"nativeType\":null,\"relationName\":\"DiscountToProductDiscount\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"discountHistory\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DiscountHistory\",\"nativeType\":null,\"relationName\":\"DiscountToDiscountHistory\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"ProductDiscount\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"productId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"product\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Product\",\"nativeType\":null,\"relationName\":\"ProductToProductDiscount\",\"relationFromFields\":[\"productId\"],\"relationToFields\":[\"id\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"discountId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"discount\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Discount\",\"nativeType\":null,\"relationName\":\"DiscountToProductDiscount\",\"relationFromFields\":[\"discountId\"],\"relationToFields\":[\"id\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"DiscountHistory\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"discountId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"changes\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"snapshot\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"change_type\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ChangeType\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"version\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"changed_by\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"discount\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Discount\",\"nativeType\":null,\"relationName\":\"DiscountToDiscountHistory\",\"relationFromFields\":[\"discountId\"],\"relationToFields\":[\"id\"],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"ProductAttribute\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"Material_Number\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"key\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"value\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"product\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Product\",\"nativeType\":null,\"relationName\":\"ProductToProductAttribute\",\"relationFromFields\":[\"Material_Number\"],\"relationToFields\":[\"Material_Number\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true}],\"primaryKey\":null,\"uniqueFields\":[[\"Material_Number\",\"key\"]],\"uniqueIndexes\":[{\"name\":null,\"fields\":[\"Material_Number\",\"key\"]}],\"isGenerated\":false},\"ProductAttributeHistory\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"Material_Number\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"changes\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"snapshot\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"change_type\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ChangeType\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"version\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"changed_by\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"product\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Product\",\"nativeType\":null,\"relationName\":\"ProductToProductAttributeHistory\",\"relationFromFields\":[\"Material_Number\"],\"relationToFields\":[\"Material_Number\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"OrderStatusHistory\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"orderId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"order\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Order\",\"nativeType\":null,\"relationName\":\"OrderToOrderStatusHistory\",\"relationFromFields\":[\"orderId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"orderStatus\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"OrderStatus\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"paymentStatus\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"PaymentStatus\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"shipmentStatus\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ShipmentStatus\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"previousOrderStatus\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"OrderStatus\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"previousPaymentStatus\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"PaymentStatus\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"previousShipmentStatus\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ShipmentStatus\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"reason\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"notes\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"changedBy\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"ipAddress\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userAgent\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"Return\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"returnNumber\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"orderId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"order\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Order\",\"nativeType\":null,\"relationName\":\"OrderToReturn\",\"relationFromFields\":[\"orderId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Restrict\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"status\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"ReturnStatus\",\"nativeType\":null,\"default\":\"requested\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"reason\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ReturnReason\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"additionalNotes\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Text\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"isApproved\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Boolean\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"approvedBy\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"approvedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"rejectionReason\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Text\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"refundAmount\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"nativeType\":[\"Decimal\",[\"15\",\"2\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"refundMethod\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"RefundMethod\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"refundedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"refundReference\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"returnShippingLabel\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"receivedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"inspectedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"returnItems\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ReturnItem\",\"nativeType\":null,\"relationName\":\"ReturnToReturnItem\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"statusHistory\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ReturnStatusHistory\",\"nativeType\":null,\"relationName\":\"ReturnToReturnStatusHistory\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdBy\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedBy\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"ReturnItem\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"returnId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"return\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Return\",\"nativeType\":null,\"relationName\":\"ReturnToReturnItem\",\"relationFromFields\":[\"returnId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"orderItemId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"orderItem\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"OrderItem\",\"nativeType\":null,\"relationName\":\"OrderItemToReturnItem\",\"relationFromFields\":[\"orderItemId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Restrict\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"quantity\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"reason\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ReturnItemReason\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"condition\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"ItemCondition\",\"nativeType\":null,\"default\":\"asDescribed\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"description\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Text\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"isReceived\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"isInspected\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"inspectionNotes\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Text\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"inspectionResult\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"InspectionResult\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true}],\"primaryKey\":null,\"uniqueFields\":[[\"returnId\",\"orderItemId\"]],\"uniqueIndexes\":[{\"name\":null,\"fields\":[\"returnId\",\"orderItemId\"]}],\"isGenerated\":false},\"ReturnStatusHistory\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"returnId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"return\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Return\",\"nativeType\":null,\"relationName\":\"ReturnToReturnStatusHistory\",\"relationFromFields\":[\"returnId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"previousStatus\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ReturnStatus\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"newStatus\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ReturnStatus\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"notes\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Text\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"changedBy\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"ServiceRequest\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"serviceNumber\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"userId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"user\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"nativeType\":null,\"relationName\":\"ServiceRequestToUser\",\"relationFromFields\":[\"userId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Restrict\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"vin\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"vehicleModel\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"vehicleYear\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"mileage\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"type\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"ServiceType\",\"nativeType\":null,\"default\":\"repair\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"status\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"ServiceStatus\",\"nativeType\":null,\"default\":\"requested\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"description\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Text\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"diagnosisNotes\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Text\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"preferredDate\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"scheduledDate\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"completedDate\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"estimatedDuration\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"estimatedCost\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"nativeType\":[\"Decimal\",[\"15\",\"2\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"finalCost\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"nativeType\":[\"Decimal\",[\"15\",\"2\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"isPaid\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"paidAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"serviceItems\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ServiceItem\",\"nativeType\":null,\"relationName\":\"ServiceItemToServiceRequest\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"statusHistory\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ServiceStatusHistory\",\"nativeType\":null,\"relationName\":\"ServiceRequestToServiceStatusHistory\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdBy\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedBy\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"Order\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Order\",\"nativeType\":null,\"relationName\":\"OrderToServiceRequest\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"ServiceItem\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"serviceRequestId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"serviceRequest\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ServiceRequest\",\"nativeType\":null,\"relationName\":\"ServiceItemToServiceRequest\",\"relationFromFields\":[\"serviceRequestId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"productId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"product\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Product\",\"nativeType\":null,\"relationName\":\"ProductToServiceItem\",\"relationFromFields\":[\"productId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"SetNull\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"description\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"quantity\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"nativeType\":null,\"default\":1,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"unitPrice\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"nativeType\":[\"Decimal\",[\"15\",\"2\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"totalPrice\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"nativeType\":[\"Decimal\",[\"15\",\"2\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"itemType\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"ServiceItemType\",\"nativeType\":null,\"default\":\"part\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"laborHours\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"nativeType\":[\"Decimal\",[\"5\",\"2\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":true}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"ServiceStatusHistory\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"serviceRequestId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"serviceRequest\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ServiceRequest\",\"nativeType\":null,\"relationName\":\"ServiceRequestToServiceStatusHistory\",\"relationFromFields\":[\"serviceRequestId\"],\"relationToFields\":[\"id\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"previousStatus\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ServiceStatus\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"newStatus\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ServiceStatus\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"notes\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"Text\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"changedBy\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"CategorySection\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"cuid\",\"args\":[1]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"description\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"image\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"href\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false}},\"enums\":{\"NotificationType\":{\"values\":[{\"name\":\"INFO\",\"dbName\":null},{\"name\":\"SUCCESS\",\"dbName\":null},{\"name\":\"WARNING\",\"dbName\":null},{\"name\":\"ERROR\",\"dbName\":null}],\"dbName\":null},\"BannerPlacement\":{\"values\":[{\"name\":\"HOME\",\"dbName\":null},{\"name\":\"CATEGORY\",\"dbName\":null},{\"name\":\"PRODUCT\",\"dbName\":null},{\"name\":\"CHECKOUT\",\"dbName\":null},{\"name\":\"SIDEBAR\",\"dbName\":null},{\"name\":\"HEADER\",\"dbName\":null},{\"name\":\"FOOTER\",\"dbName\":null},{\"name\":\"POPUP\",\"dbName\":null},{\"name\":\"HERO\",\"dbName\":null},{\"name\":\"CATEGORY_SECTION_LANDING_PAGE\",\"dbName\":null}],\"dbName\":null},\"DeviceTarget\":{\"values\":[{\"name\":\"ALL\",\"dbName\":null},{\"name\":\"DESKTOP\",\"dbName\":null},{\"name\":\"MOBILE\",\"dbName\":null},{\"name\":\"TABLET\",\"dbName\":null}],\"dbName\":null},\"ChangeType\":{\"values\":[{\"name\":\"CRON_DISCOUNT_EXPIRED\",\"dbName\":null},{\"name\":\"MANUAL_DISCOUNT_EXPIRED_BUTTON_PRESS\",\"dbName\":null},{\"name\":\"INSERT\",\"dbName\":null},{\"name\":\"UPDATE\",\"dbName\":null},{\"name\":\"DELETE\",\"dbName\":null},{\"name\":\"ORDER\",\"dbName\":null},{\"name\":\"DISCOUNT\",\"dbName\":null},{\"name\":\"PRODUCT_ADDED\",\"dbName\":null},{\"name\":\"PRODUCT_DELETE_ALL\",\"dbName\":null},{\"name\":\"PRODUCT_DELETE\",\"dbName\":null},{\"name\":\"PRODUCT_ADD_TO_DISCOUNT_WITH_CSV\",\"dbName\":null},{\"name\":\"ADDED_TO_DISCOUNT\",\"dbName\":null},{\"name\":\"DELETED_FROM_DISCOUNT\",\"dbName\":null}],\"dbName\":null},\"DiscountType\":{\"values\":[{\"name\":\"PERCENTAGE\",\"dbName\":null},{\"name\":\"FIXED_AMOUNT\",\"dbName\":null},{\"name\":\"NEW_PRICE\",\"dbName\":null}],\"dbName\":null},\"MisterMiss\":{\"values\":[{\"name\":\"Dl\",\"dbName\":null},{\"name\":\"Dna\",\"dbName\":null}],\"dbName\":null},\"Rol\":{\"values\":[{\"name\":\"administAB\",\"dbName\":null},{\"name\":\"moderatorAB\",\"dbName\":null},{\"name\":\"inregistratAB\",\"dbName\":null},{\"name\":\"fourLvlAdminAB\",\"dbName\":null},{\"name\":\"fourLvlInregistratAB\",\"dbName\":null},{\"name\":\"angajatAB\",\"dbName\":null}],\"dbName\":null},\"Showroom\":{\"values\":[{\"name\":\"CJ\",\"dbName\":null},{\"name\":\"BV\",\"dbName\":null},{\"name\":\"TM\",\"dbName\":null},{\"name\":\"AR\",\"dbName\":null},{\"name\":\"BAC\",\"dbName\":null},{\"name\":\"BAN\",\"dbName\":null},{\"name\":\"OTP\",\"dbName\":null},{\"name\":\"MIL\",\"dbName\":null},{\"name\":\"TGM\",\"dbName\":null},{\"name\":\"JIL\",\"dbName\":null},{\"name\":\"CT\",\"dbName\":null},{\"name\":\"CRA\",\"dbName\":null},{\"name\":\"SB\",\"dbName\":null}],\"dbName\":null},\"OrderStatus\":{\"values\":[{\"name\":\"plasata\",\"dbName\":null},{\"name\":\"procesare\",\"dbName\":null},{\"name\":\"confirmata\",\"dbName\":null},{\"name\":\"pregatita\",\"dbName\":null},{\"name\":\"expediata\",\"dbName\":null},{\"name\":\"livrata\",\"dbName\":null},{\"name\":\"completa\",\"dbName\":null},{\"name\":\"anulata\",\"dbName\":null},{\"name\":\"stornata\",\"dbName\":null},{\"name\":\"returnata\",\"dbName\":null},{\"name\":\"partiala\",\"dbName\":null}],\"dbName\":null},\"ShippingMethod\":{\"values\":[{\"name\":\"curier\",\"dbName\":null},{\"name\":\"showroom\",\"dbName\":null}],\"dbName\":null},\"ShipmentStatus\":{\"values\":[{\"name\":\"asteptare\",\"dbName\":null},{\"name\":\"prelucrare\",\"dbName\":null},{\"name\":\"pregatit\",\"dbName\":null},{\"name\":\"expediat\",\"dbName\":null},{\"name\":\"tranzit\",\"dbName\":null},{\"name\":\"livrat\",\"dbName\":null},{\"name\":\"esuat\",\"dbName\":null},{\"name\":\"intors\",\"dbName\":null},{\"name\":\"anulat\",\"dbName\":null},{\"name\":\"partial\",\"dbName\":null}],\"dbName\":null},\"PaymentMethod\":{\"values\":[{\"name\":\"ramburs\",\"dbName\":null},{\"name\":\"card\",\"dbName\":null},{\"name\":\"transfer\",\"dbName\":null},{\"name\":\"laTermen\",\"dbName\":null}],\"dbName\":null},\"PaymentStatus\":{\"values\":[{\"name\":\"asteptare\",\"dbName\":null},{\"name\":\"succes\",\"dbName\":null},{\"name\":\"esuat\",\"dbName\":null},{\"name\":\"rambursat\",\"dbName\":null},{\"name\":\"partial_rambursat\",\"dbName\":null},{\"name\":\"contestat\",\"dbName\":null}],\"dbName\":null},\"StockStatus\":{\"values\":[{\"name\":\"IN_STOCK\",\"dbName\":null},{\"name\":\"LOW_STOCK\",\"dbName\":null},{\"name\":\"OUT_OF_STOCK\",\"dbName\":null},{\"name\":\"DISCONTINUED\",\"dbName\":null},{\"name\":\"UNKNOWN\",\"dbName\":null}],\"dbName\":null},\"ReturnStatus\":{\"values\":[{\"name\":\"requested\",\"dbName\":null},{\"name\":\"approved\",\"dbName\":null},{\"name\":\"rejected\",\"dbName\":null},{\"name\":\"awaitingReceipt\",\"dbName\":null},{\"name\":\"received\",\"dbName\":null},{\"name\":\"inspected\",\"dbName\":null},{\"name\":\"refundIssued\",\"dbName\":null},{\"name\":\"completed\",\"dbName\":null},{\"name\":\"cancelled\",\"dbName\":null}],\"dbName\":null},\"ReturnReason\":{\"values\":[{\"name\":\"wrongItem\",\"dbName\":null},{\"name\":\"defective\",\"dbName\":null},{\"name\":\"damaged\",\"dbName\":null},{\"name\":\"notAsDescribed\",\"dbName\":null},{\"name\":\"noLongerWanted\",\"dbName\":null},{\"name\":\"other\",\"dbName\":null}],\"dbName\":null},\"ReturnItemReason\":{\"values\":[{\"name\":\"wrongItem\",\"dbName\":null},{\"name\":\"defective\",\"dbName\":null},{\"name\":\"damaged\",\"dbName\":null},{\"name\":\"notAsDescribed\",\"dbName\":null},{\"name\":\"noLongerWanted\",\"dbName\":null},{\"name\":\"other\",\"dbName\":null}],\"dbName\":null},\"ItemCondition\":{\"values\":[{\"name\":\"asDescribed\",\"dbName\":null},{\"name\":\"damaged\",\"dbName\":null},{\"name\":\"opened\",\"dbName\":null},{\"name\":\"used\",\"dbName\":null},{\"name\":\"missingParts\",\"dbName\":null}],\"dbName\":null},\"InspectionResult\":{\"values\":[{\"name\":\"approved\",\"dbName\":null},{\"name\":\"rejected\",\"dbName\":null},{\"name\":\"partiallyApproved\",\"dbName\":null}],\"dbName\":null},\"RefundMethod\":{\"values\":[{\"name\":\"originalPayment\",\"dbName\":null},{\"name\":\"storeCredit\",\"dbName\":null},{\"name\":\"bankTransfer\",\"dbName\":null}],\"dbName\":null},\"ServiceType\":{\"values\":[{\"name\":\"repair\",\"dbName\":null},{\"name\":\"maintenance\",\"dbName\":null},{\"name\":\"warranty\",\"dbName\":null},{\"name\":\"installation\",\"dbName\":null},{\"name\":\"inspection\",\"dbName\":null},{\"name\":\"other\",\"dbName\":null}],\"dbName\":null},\"ServiceStatus\":{\"values\":[{\"name\":\"requested\",\"dbName\":null},{\"name\":\"scheduled\",\"dbName\":null},{\"name\":\"inProgress\",\"dbName\":null},{\"name\":\"diagnosisComplete\",\"dbName\":null},{\"name\":\"awaitingParts\",\"dbName\":null},{\"name\":\"awaitingApproval\",\"dbName\":null},{\"name\":\"completed\",\"dbName\":null},{\"name\":\"cancelled\",\"dbName\":null},{\"name\":\"delivered\",\"dbName\":null}],\"dbName\":null},\"ServiceItemType\":{\"values\":[{\"name\":\"part\",\"dbName\":null},{\"name\":\"labor\",\"dbName\":null},{\"name\":\"fee\",\"dbName\":null},{\"name\":\"other\",\"dbName\":null}],\"dbName\":null}},\"types\":{}}")
defineDmmfProperty(exports.Prisma, config.runtimeDataModel)
config.engineWasm = undefined
config.compilerWasm = undefined


const { warnEnvConflicts } = require('./runtime/library.js')

warnEnvConflicts({
    rootEnvPath: config.relativeEnvPaths.rootEnvPath && path.resolve(config.dirname, config.relativeEnvPaths.rootEnvPath),
    schemaEnvPath: config.relativeEnvPaths.schemaEnvPath && path.resolve(config.dirname, config.relativeEnvPaths.schemaEnvPath)
})

const PrismaClient = getPrismaClient(config)
exports.PrismaClient = PrismaClient
Object.assign(exports, Prisma)

// file annotations for bundling tools to include these files
path.join(__dirname, "query_engine-windows.dll.node");
path.join(process.cwd(), "generated/prisma/query_engine-windows.dll.node")
// file annotations for bundling tools to include these files
path.join(__dirname, "schema.prisma");
path.join(process.cwd(), "generated/prisma/schema.prisma")
