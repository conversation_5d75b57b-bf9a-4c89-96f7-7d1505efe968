"use client";

import { useState, useTransition } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2 } from "lucide-react";
import { ClerkProfileInput, clerkProfileSchema } from "@/lib/zod";
import { toast } from "sonner";
import { useReverification, useUser } from "@clerk/nextjs";
import { AccountSettingsData } from "@/app/getData/account-settings";
import { getClerkErrorMessage } from "@/lib/password";
import { isClerkRuntimeError, isReverificationCancelledError } from "@clerk/nextjs/errors";


export default function ProfileForm( { accountData }: { accountData: AccountSettingsData; }) {
  const [isPending, startTransition] = useTransition();
  const { user } = useUser();

  const {
    register,
    handleSubmit,
    formState: { errors, isDirty, dirtyFields }
  } = useForm<ClerkProfileInput>({
    resolver: zodResolver(clerkProfileSchema),
    defaultValues: {
      firstName: accountData.firstName, 
      lastName: accountData.lastName ,
      email: accountData.email ,
    }
  });


  const changeFirstAndLastName = useReverification((firstName: string, lastName: string) => {
    if (!user) return;
    return user.update({ firstName, lastName });
  })

  const onSubmit = (data: ClerkProfileInput) => {
    if (!user) return;

    // Handle name updates
    startTransition(async () => {
      let promises = [];
      let successMessages = [];

      try {
        if (dirtyFields.firstName || dirtyFields.lastName) {
          promises.push(changeFirstAndLastName(data.firstName, data.lastName));
          successMessages.push("Informațiile personale au fost actualizate.");
        }
        await Promise.all(promises);
        if (successMessages.length > 0) {
          toast.success(successMessages.join(' '));
        }
      } catch (e) {
        if (isClerkRuntimeError(e) && isReverificationCancelledError(e)) {
          console.error('User cancelled reverification', e.code)
        }
        toast.error(getClerkErrorMessage(e));
      }
    });
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Informații Personale</CardTitle>
          <CardDescription>
            Aceste informații sunt gestionate de sistemul de autentificare.
          </CardDescription>
        </CardHeader>
        <CardContent>
          
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
            {/* Form fields remain the same */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="md:col-span-2 space-y-4">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName">Prenume *</Label>
                    <Input
                      id="firstName"
                      {...register("firstName")}
                      className={errors.firstName ? "border-red-500" : ""}
                      disabled={isPending}
                    />
                    {errors.firstName && (
                      <p className="text-sm text-red-600">{errors.firstName.message}</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="lastName">Nume de familie *</Label>
                    <Input
                      id="lastName"
                      {...register("lastName")}
                      className={errors.lastName ? "border-red-500" : ""}
                      disabled={isPending}
                    />
                    {errors.lastName && (
                      <p className="text-sm text-red-600">{errors.lastName.message}</p>
                    )}
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">
                    Email * 
                    {dirtyFields.email && (
                      <span className="text-sm text-gray-500 ml-2">
                        (necesită verificare)
                      </span>
                    )}
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    {...register("email")}
                    className={errors.email ? "border-red-500" : ""}
                    disabled={true}
                  />
                </div>
              </div>
            </div>
            <div className="flex justify-end pt-4 border-t">
              <Button
                type="submit"
                disabled={isPending || !isDirty}
                className="bg-[#0066B1] hover:bg-[#004d85] text-white"
              >
                {isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Se actualizează...
                  </>
                ) : (
                  "Actualizează informațiile personale"
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
