"use client"

import React, { useState, useEffect, useRef } from "react";
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";

interface SearchResult {
  id: number;
  name: string;
  category: string;
}

// Mock search results for demonstration
const mockSearchResults = [
  { id: 1, name: "M Sport Brake Kit", category: "Brakes" },
  { id: 2, name: "KW V3 Coilover Kit", category: "Suspension" },
  { id: 3, name: "Akrapovič Titanium Exhaust", category: "Exhaust" },
  { id: 4, name: "BBS FI-R Forged Wheels", category: "Wheels" },
  { id: 5, name: "Recaro Sportster CS", category: "Interior" },
  { id: 6, name: "M Performance Air Intake", category: "Performance" },
  { id: 7, name: "Dinan Stage 2 Kit", category: "Performance" },
  { id: 8, name: "M Performance Steering Wheel", category: "Interior" },
  { id: 9, name: "AC Schnitzer Rear Wing", category: "Exterior" },
  { id: 10, name: "Bilstein PSS10 Coilover Kit", category: "Suspension" },
];

const SearchSection = ({
  onSearch = () => {},
  onPartNumberLookup = () => {},
  placeholder = "Search parts or enter part number...",
  query,
}: { query?: string ,
  onSearch?: (searchTerm: string) => void;
  onPartNumberLookup?: (partNumber: string) => void;
  placeholder?: string;
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [showResults, setShowResults] = useState(false);
  const [filteredResults, setFilteredResults] = useState<SearchResult[]>([]);
  const searchRef = useRef<HTMLDivElement>(null);
  const navigate = useRouter();

  useEffect(() => {
    if (searchTerm.length > 1) {
      const filtered = mockSearchResults
        .filter((item) =>
          item.name.toLowerCase().includes(searchTerm.toLowerCase()),
        )
        .slice(0, 5);
      setFilteredResults(filtered);
      setShowResults(true);
    } else {
      setShowResults(false);
    }
  }, [searchTerm]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowResults(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchTerm.trim()) {
      navigate.push(`/search?q=${encodeURIComponent(searchTerm)}`);
      onSearch(searchTerm);
      setShowResults(false);
    }
  };

  const handleResultClick = (result: SearchResult) => {
    navigate.push(`/product/m-sport-brake-kit`);
    setShowResults(false);
  };

  return (
    <div
      className="w-full max-w-2xl mx-auto p-2 md:p-4"
      ref={searchRef}
    >
      <form
        onSubmit={handleSearch}
        className="flex flex-wrap md:flex-nowrap gap-2 relative"
      >
        <div className="relative flex-1">
          <Input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder={placeholder}
            className="w-full pl-10 pr-4 h-12 text-base"
          />
          <Search
            className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
            size={20}
          />

          {showResults && filteredResults.length > 0 && (
            <div className="absolute top-full left-0 right-0 mt-1 shadow-lg rounded-md z-50 border border-gray-200">
              {filteredResults.map((result) => (
                <div
                  key={result.id}
                  className="p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-0 flex items-center gap-3"
                >
                  <div className="h-16 w-16 flex-shrink-0 rounded overflow-hidden bg-gray-100">
                    <img
                      src={`https://images.unsplash.com/photo-${1550000000 + result.id * 10000}?w=100&h=100&fit=crop&q=80`}
                      alt={result.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div
                    className="flex-1 min-w-0"
                    onClick={() => handleResultClick(result)}
                  >
                    <div className="font-medium truncate">{result.name}</div>
                    <div className="text-xs text-gray-500 flex items-center gap-1">
                      <span className="bg-gray-100 px-1.5 py-0.5 rounded text-xs">
                        {result.category}
                      </span>
                      <span className="text-xs text-gray-400">|</span>
                      <span className="text-xs">
                        Cod:{" "}
                        {result.id < 10
                          ? `BMW-00${result.id}`
                          : `BMW-0${result.id}`}
                      </span>
                    </div>
                    <div className="text-sm font-semibold mt-1 text-[#0066B1]">
                      RON{(result.id * 100 + 99.99).toFixed(2)}
                    </div>
                  </div>
                  <button
                    className="bg-[#0066B1] hover:bg-[#004d85] text-white p-2 rounded-full flex-shrink-0 transition-colors"
                    onClick={(e) => {
                      e.stopPropagation();
                      // Add to cart logic would go here
                      alert(`Added ${result.name} to cart`);
                    }}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <circle cx="9" cy="21" r="1"></circle>
                      <circle cx="20" cy="21" r="1"></circle>
                      <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
                    </svg>
                  </button>
                </div>
              ))}
              <div
                className="p-2 bg-gray-50 text-center text-sm text-[#0066B1] hover:underline cursor-pointer"
                onClick={handleSearch}
              >
                Vezi toate rezultatele pentru "{searchTerm}"...
              </div>
            </div>
          )}
        </div>

        <Button
          type="submit"
          className="h-12 px-6 bg-[#0066B1] hover:bg-[#004d85] text-white"
        >
          Cauta
        </Button>
      </form>
    </div>
  );
};

export default SearchSection;


//MERGE
// import React from "react";
// import { Search, ScanLine } from "lucide-react";
// import { Input } from "@/components/ui/input";
// import { Button } from "@/components/ui/button";

// interface SearchSectionProps {
//   onSearch?: (searchTerm: string) => void;
//   onPartNumberLookup?: (partNumber: string) => void;
//   placeholder?: string;
// }

// const SearchSection = ({
//   onSearch = () => {},
//   onPartNumberLookup = () => {},
//   placeholder = "Search parts or enter part number...",
// }: SearchSectionProps) => {
//   const [searchTerm, setSearchTerm] = React.useState("");

//   const handleSearch = (e: React.FormEvent) => {
//     e.preventDefault();
//     onSearch(searchTerm);
//   };

//   const handlePartNumberLookup = () => {
//     onPartNumberLookup(searchTerm);
//   };

//   return (
//     <div className="w-full max-w-2xl mx-auto bg-white p-2 md:p-4">
//       <form
//         onSubmit={handleSearch}
//         className="flex flex-wrap md:flex-nowrap gap-2"
//       >
//         <div className="relative flex-1">
//           <Input
//             type="text"
//             value={searchTerm}
//             onChange={(e) => setSearchTerm(e.target.value)}
//             placeholder={placeholder}
//             className="w-full pl-10 pr-4 h-12 text-base"
//           />
//           <Search
//             className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
//             size={20}
//           />
//         </div>

//         <Button
//           type="submit"
//           className="h-12 px-6 bg-[#0066B1] hover:bg-[#004d85] text-white"
//         >
//           Search
//         </Button>
//       </form>
//     </div>
//   );
// };

// export default SearchSection;
