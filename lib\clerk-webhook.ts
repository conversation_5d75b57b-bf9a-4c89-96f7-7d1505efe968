"server-only"

import { ReadonlyHeaders } from "next/dist/server/web/spec-extension/adapters/headers";
import { prisma, withRetry } from "./db";
import { logger } from "./logger";
import { WebhookEvent } from "@clerk/nextjs/server";
import { User<PERSON><PERSON><PERSON>, DeletedObjectJSON } from "@clerk/nextjs/server";
import { findOrCreateUser } from "./sync-user";
import { isValidEmail, isValidImageUrl, isValidName, maskEmail } from "./utils";

// Define types for our data structures
interface ExistingUser {
  id: string;
  email: string;
  firstName: string | null;
  lastName: string | null;
  profileImage: string | null;
  passwordEnabled: boolean | null;
  twoFactorEnabled: boolean | null;
  updatedAt: Date;
  mfaEnabledAt: Date | null;
  mfaDisabledAt: Date | null;
  totpEnabled: boolean | null;
  emailVerified: Date | null;
}

interface UserUpdateData {
  email?: string;
  firstName?: string;
  lastName?: string;
  profileImage?: string;
  passwordEnabled?: boolean;
  twoFactorEnabled?: boolean;
  mfaEnabledAt?: Date | null;
  mfaDisabledAt?: Date | null;
  totpEnabled?: boolean;
  emailVerified?: Date | null;
}

interface AuditLogEntry {
  action: string;
  details: string;
}

interface AuditLogData {
  userId: string;
  action: string;
  entityType: string;
  entityId: string;
  details: string;
  ipAddress: string | null;
  userAgent: string | null;
  createdAt: Date;
  performedBy: string;
}

interface DetectedChanges {
  hasChanges: boolean;
  updateData: UserUpdateData;
  auditLogs: AuditLogEntry[];
  changeTypes: string[];
}

interface ClerkPhoneNumber {
  id: string;
  phone_number: string;
  verification?: {
    status: string;
    strategy?: string;
  };
}

// Define specific webhook event types using type intersections
type UserUpdatedEvent = WebhookEvent & {
  type: 'user.updated' | 'user.created';
  data: ExtendedUserJSON;
};

//extend UserJSON with the new fields : mfa_enabled_at, mfa_disabled_at TEST
interface ExtendedUserJSON extends UserJSON {
  mfa_enabled_at: Date | null;
  mfa_disabled_at: Date | null;
}

type UserDeletedEvent = WebhookEvent & {
  type: 'user.deleted';
  data: DeletedObjectJSON;
};

export async function handleUserCreated(
  evt: WebhookEvent, 
  headerPayload: ReadonlyHeaders
): Promise<void> {
  // Type assertion to ensure we have the right event type
  const createEvent = evt as UserUpdatedEvent;
  const { id, email_addresses, first_name, last_name, image_url, phone_numbers, two_factor_enabled, totp_enabled, password_enabled, mfa_enabled_at, mfa_disabled_at } = createEvent.data;
  
  try {
    const dbUser = await findOrCreateUser({
      externalId: id as string,
      email: email_addresses?.[0]?.email_address || '',
      firstName: first_name || '',
      lastName: last_name || '',
      profileImage: image_url || '',
      updateLoginStats: false, // Don't update login stats for webhook events
      twoFactorEnabled: two_factor_enabled || false,
      phoneNumber: phone_numbers?.[0]?.phone_number || '',
      mfaEnabledAt: mfa_enabled_at || null,
      mfaDisabledAt: mfa_disabled_at || null,
      passwordEnabled: password_enabled || null,
      totpEnabled: totp_enabled || false,
      emailVerified: email_addresses?.[0]?.verification?.status === 'verified' ? new Date() : null,
    });
    
    if (!dbUser) {
      logger.warn(`[Clerk Webhook] User not created for user.created: ${id}`);
      return;
    }
    
    // Create audit log for user creation
    await withRetry(() => prisma.userAuditLog.create({
      data: {
        userId: dbUser.id,
        action: 'user.created',
        entityType: 'user',
        entityId: id as string,
        details: JSON.stringify({
          message: 'User account created',
          email: email_addresses?.[0]?.email_address,
          firstName: first_name,
          lastName: last_name,
          hasProfileImage: !!image_url,
          timestamp: new Date().toISOString()
        }),
        ipAddress: headerPayload.get('x-forwarded-for') || null,
        userAgent: headerPayload.get('user-agent') || null,
        performedBy: 'clerkWebhook',
      }
    }));
    
    logger.info(`[Clerk Webhook] user.created processed for ID: ${id}`);
  } catch (error) {
    logger.error(`[Clerk Webhook] Error processing user.created for ID: ${id}:`, error);
    throw error;
  }
}

export async function handleUserUpdated(
  evt: WebhookEvent, 
  headerPayload: ReadonlyHeaders
): Promise<void> {

  // Type assertion to ensure we have the right event type
  const userEvent = evt as UserUpdatedEvent;
  
  const { id } = userEvent.data;

  // Get current user data for comparison
  const existingUser = await withRetry(() => 
    prisma.user.findUnique({
      where: { externalId: id },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        profileImage: true,
        twoFactorEnabled: true,
        updatedAt: true,
        mfaEnabledAt: true,
        mfaDisabledAt: true,
        passwordEnabled: true,
        totpEnabled: true,
        emailVerified: true,
      }
    })
  );

  if (!existingUser) {
    logger.warn(`[Clerk Webhook] User not found for update: ${id}`);
    return;
  }

  // Detect and validate changes
  const changes = await detectAndValidateChanges(userEvent.data, existingUser);
  
  if (changes.hasChanges) {
    // Update only changed fields
    const updatedUser = await withRetry(() => prisma.user.update({
      where: { externalId: id },
      data: {
        ...changes.updateData,
        updatedAt: new Date(),
      },
    }));

    // Create specific audit logs for each change
    await createAuditLogsForChanges(changes.auditLogs, updatedUser.id, headerPayload);
    
    logger.info(`[Clerk Webhook] user.updated processed for ID: ${id}, changes: ${changes.changeTypes.join(', ')}`);
  } else {
    logger.debug(`[Clerk Webhook] user.updated received for ID: ${id} but no relevant changes detected`);
  }
}

export async function handleUserDeleted(
  evt: WebhookEvent, 
  headerPayload: ReadonlyHeaders
): Promise<void> {
  // Type assertion to ensure we have the right event type
  const deleteEvent = evt as UserDeletedEvent;
  const { id } = deleteEvent.data;

  if(!id){
    logger.warn(`[Clerk Webhook] No clerkId provided to delete`);
    return;
  }

  try {

    //get the userId from our database
    const userId = await withRetry(() => prisma.user.findUnique({
      where: { externalId: id },
      select: { id: true, email: true }
    }));

    if (!userId) {
      logger.warn(`[Clerk Webhook] User not found in database to delete the clerkId: ${id}`);
      return;
    }

    // Soft delete making the account inactive
    await withRetry(() => prisma.user.update({
      where: { id: userId.id },
      data: {
        //we dont delete the account, we make it inactive for 30 days, the trigger  will delete it or the admin will activate it
        isActive: false,
        inactiveBy: userId.email,
        inactiveAt: new Date(),
        inactiveReason: 'Deleted from Clerk',
      },
    }));

    // Create audit log
    await withRetry(() => prisma.userAuditLog.create({
      data: {
        userId: id,
        action: 'user.deleted',
        entityType: 'user',
        entityId: id,
        details: JSON.stringify({
          message: `User account deleted from clerk but not from our database(is inactive). clerkId: ${id} and userId: ${userId.id} and email ${userId.email}`,
          timestamp: new Date().toISOString()
        }),
        ipAddress: headerPayload.get('x-forwarded-for') || null,
        userAgent: headerPayload.get('user-agent') || null,
        performedBy: 'clerkWebhook', // Set the performedBy field to 'clerkWebhook' for audit purposes
      }
    }));

    logger.info(`[Clerk Webhook] user.deleted processed for ID: ${id}`);
  } catch (error) {
    logger.error(`[Clerk Webhook] Error processing user.deleted for ID: ${id}:`, error);
    throw error;
  }
}

async function detectAndValidateChanges(
  eventData: ExtendedUserJSON, 
  existingUser: ExistingUser
): Promise<DetectedChanges> {
  const changes: DetectedChanges = {
    hasChanges: false,
    updateData: {},
    auditLogs: [],
    changeTypes: []
  };

  const timestamp = new Date();

// Assuming 'eventData' is the payload from the Clerk webhook and 'existingUser' is your user record from the database.

const primaryEmailObject = eventData.email_addresses?.find(
  (e) => e.id === eventData.primary_email_address_id
);

if (primaryEmailObject) {
  const newEmail = primaryEmailObject.email_address;
  const isVerified = primaryEmailObject.verification?.status === 'verified';

  // Check if the email address in your DB is different from the new primary email from Clerk.
  if (newEmail && newEmail !== existingUser.email) {
    changes.updateData.email = newEmail;
    changes.hasChanges = true;
    changes.changeTypes.push('email');
    changes.auditLogs.push({
      action: 'email.change',
      details: JSON.stringify({
        message: `Email address changed from ${existingUser.email} to ${newEmail}`,
        previousEmail: maskEmail(existingUser.email),
        newEmail: maskEmail(newEmail),
      }),
    });

    // Since the email has changed, we must also update its verification status.
    // This ensures the verification status always corresponds to the *current* email.
    const newVerificationStatus = isVerified ? new Date() : null;
    if (newVerificationStatus !== existingUser.emailVerified) {
      changes.updateData.emailVerified = newVerificationStatus;
      // No need to push a separate audit log here as it's part of the email change event.
    }
  } else {
    // This block handles the case where the email address is the same,
    // but its verification status might have changed.
    const newVerificationStatus = isVerified ? new Date() : null;
    const wasVerified = !!existingUser.emailVerified;

    if (newVerificationStatus !== null && !wasVerified) {
      changes.updateData.emailVerified = newVerificationStatus;
      changes.hasChanges = true;
      changes.changeTypes.push('email.verified');
      changes.auditLogs.push({
        action: 'email.verified',
        details: JSON.stringify({
          message: `Email ${newEmail} was verified.`,
        }),
      });
    }
  }
}

  // // Email change detection
  // const newEmail = eventData.email_addresses?.[0]?.email_address;
  // const isEmailVerifiedForNewEmail = eventData.email_addresses?.[0]?.verification?.status === 'verified' && newEmail !== existingUser.email;
  // if (newEmail && newEmail !== existingUser.email && isValidEmail(newEmail) && !existingUser.emailVerified && isEmailVerifiedForNewEmail) {
  //   changes.updateData.email = newEmail;
  //   changes.updateData.emailVerified = new Date(); // Set emailVerified to current date if email changes
  //   changes.hasChanges = true;
  //   changes.changeTypes.push('email');
  //   changes.auditLogs.push({
  //     action: 'email.change',
  //     details: JSON.stringify({
  //       message: `Email address changed from ${existingUser.email} to ${newEmail}`,
  //       previousEmail: maskEmail(existingUser.email),
  //       newEmail: maskEmail(newEmail),
  //       timestamp: timestamp.toISOString()
  //     })
  //   });
  // }

  // // Email verification change detection
  // const wasEmailVerified = !!existingUser.emailVerified; // true if Date, false if null or undefined

  // if (isEmailVerifiedForNewEmail !== undefined && isEmailVerifiedForNewEmail !== wasEmailVerified) {
  //   changes.updateData.emailVerified = isEmailVerifiedForNewEmail ? new Date() : null;
  //   changes.hasChanges = true;
  //   changes.changeTypes.push('emailVerification');
  //   changes.auditLogs.push({
  //     action: isEmailVerifiedForNewEmail ? 'email.verified' : 'email.unverified',
  //     details: JSON.stringify({
  //       message: isEmailVerifiedForNewEmail ? `Email verified for ${newEmail}` : `Email unverified for ${newEmail}`,
  //       timestamp: timestamp.toISOString()
  //     })
  //   });
  // }

  // First name change detection
  const newFirstName = eventData.first_name;
  if (newFirstName !== undefined && 
      newFirstName !== existingUser.firstName && 
      isValidName(newFirstName)) {
    changes.updateData.firstName = newFirstName || '';
    changes.hasChanges = true;
    changes.changeTypes.push('firstName');
    changes.auditLogs.push({
      action: 'profile.firstName.change',
      details: JSON.stringify({
        message: `First name changed from ${existingUser.firstName} to ${newFirstName}`,
        timestamp: timestamp.toISOString()
      })
    });
  }

  // Last name change detection
  const newLastName = eventData.last_name;
  if (newLastName !== undefined && 
      newLastName !== existingUser.lastName && 
      isValidName(newLastName)) {
    changes.updateData.lastName = newLastName || '';
    changes.hasChanges = true;
    changes.changeTypes.push('lastName');
    changes.auditLogs.push({
      action: 'profile.lastName.change',
      details: JSON.stringify({
        message: `Last name changed from ${existingUser.lastName} to ${newLastName}`,
        timestamp: timestamp.toISOString()
      })
    });
  }

  // Profile image change detection
  const newImageUrl = eventData.image_url;
  if (newImageUrl !== undefined && 
      newImageUrl !== existingUser.profileImage && 
      isValidImageUrl(newImageUrl)) {
    changes.updateData.profileImage = newImageUrl || '';
    changes.hasChanges = true;
    changes.changeTypes.push('profileImage');
    changes.auditLogs.push({
      action: 'profile.image.change',
      details: JSON.stringify({
        message: `Profile image changed from ${existingUser.profileImage} to ${newImageUrl}`,
        hasImage: !!newImageUrl,
        timestamp: timestamp.toISOString()
      })
    });
  }

  // Password change detection
  if (eventData.password_enabled !== undefined && 
      eventData.password_enabled !== existingUser.passwordEnabled) {
    changes.updateData.passwordEnabled = eventData.password_enabled;
    changes.hasChanges = true;
    changes.changeTypes.push('password');
    changes.auditLogs.push({
      action: 'password.change',
      details: JSON.stringify({
        message: `Password changed`,
        timestamp: timestamp.toISOString(),
        method: 'clerk_user_profile'
      })
    });
  }

  // TOTP change detection
  if (eventData.totp_enabled !== undefined && 
      eventData.totp_enabled !== existingUser.totpEnabled) {
    changes.updateData.totpEnabled = eventData.totp_enabled;
    changes.hasChanges = true;
    changes.changeTypes.push('totp');
    changes.auditLogs.push({
      action: 'totp.change',
      details: JSON.stringify({
        message: `TOTP changed`,
        timestamp: timestamp.toISOString(),
        method: 'clerk_user_profile'
      })
    });
  }

  // MFA enabled at change detection
  if (eventData.mfa_enabled_at !== undefined && 
      eventData.mfa_enabled_at !== existingUser.mfaEnabledAt) {
    changes.updateData.mfaEnabledAt = eventData.mfa_enabled_at;
    changes.hasChanges = true;
    changes.changeTypes.push('mfaEnabledAt');
    changes.auditLogs.push({
      action: 'mfa.enabled',
      details: JSON.stringify({
        message: `MFA enabled`,
        timestamp: timestamp.toISOString(),
        method: 'clerk_user_profile'
      })
    });
  }

  // MFA disabled at change detection
  if (eventData.mfa_disabled_at !== undefined && 
      eventData.mfa_disabled_at !== existingUser.mfaDisabledAt) {
    changes.updateData.mfaDisabledAt = eventData.mfa_disabled_at;
    changes.hasChanges = true;
    changes.changeTypes.push('mfaDisabledAt');
    changes.auditLogs.push({
      action: 'mfa.disabled',
      details: JSON.stringify({
        message: `MFA disabled`,
        timestamp: timestamp.toISOString(),
        method: 'clerk_user_profile'
      })
    });
  }


  // Two-factor authentication change detection
  if (eventData.two_factor_enabled !== undefined && 
      eventData.two_factor_enabled !== existingUser.twoFactorEnabled) {
    changes.updateData.twoFactorEnabled = eventData.two_factor_enabled;
    changes.hasChanges = true;
    changes.changeTypes.push('2fa');
    changes.auditLogs.push({
      action: eventData.two_factor_enabled ? '2fa.enabled' : '2fa.disabled',
      details: JSON.stringify({
        message: eventData.two_factor_enabled ? 
          'Two-factor authentication enabled' : 
          'Two-factor authentication disabled',
        timestamp: timestamp.toISOString()
      })
    });
  }

  // Phone number verification detection
  if (eventData.phone_numbers && Array.isArray(eventData.phone_numbers)) {
    const phoneNumbers = eventData.phone_numbers as ClerkPhoneNumber[];
    const verifiedPhones = phoneNumbers.filter(
      (phone: ClerkPhoneNumber) => phone.verification?.status === 'verified'
    );
    
    if (verifiedPhones.length > 0) {
      changes.changeTypes.push('phone');
      changes.auditLogs.push({
        action: 'phone.verified',
        details: JSON.stringify({
          message: 'Phone number verified',
          phoneCount: verifiedPhones.length,
          timestamp: timestamp.toISOString()
        })
      });
    }
  }

  return changes;
}

async function createAuditLogsForChanges(
  auditLogs: AuditLogEntry[], 
  userId: string, 
  headerPayload: ReadonlyHeaders
): Promise<void> {
  if (auditLogs.length === 0) return;

  const auditData: AuditLogData[] = auditLogs.map((log: AuditLogEntry) => ({
    userId: userId,
    action: log.action,
    entityType: 'user',
    entityId: userId,
    details: log.details,
    ipAddress: headerPayload.get('x-forwarded-for') || null,
    userAgent: headerPayload.get('user-agent') || null,
    createdAt: new Date(),
    performedBy: 'clerkWebhook',
  }));

  await withRetry(() => prisma.userAuditLog.createMany({
    data: auditData
  }));
}