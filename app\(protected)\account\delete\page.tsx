'use client';

import { useUser } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Trash2, AlertTriangle, CheckCircle, AlertCircle } from "lucide-react";
import { deleteAccount } from "@/app/actions/account";

export default function AccountDeletePage() {
  const { user, isLoaded } = useUser();
  const router = useRouter();
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [reason, setReason] = useState('');
  const [confirmationText, setConfirmationText] = useState('');
  const [showFinalConfirmation, setShowFinalConfirmation] = useState(false);
  const [confirmations, setConfirmations] = useState({
    understand: false,
    dataLoss: false,
    permanent: false,
    noRecovery: false
  });

  // useEffect(() => {
  //   if (isLoaded && !user) {
  //     router.push('/sign-in');
  //   }
  // }, [isLoaded, user, router]);

  const canProceed = Object.values(confirmations).every(Boolean) && reason.trim().length > 0;
  const canDelete = canProceed && confirmationText === 'STERGE CONTUL MEU';

  const handleDelete = async () => {
    if (!user || !canDelete) return;

    setIsDeleting(true);
    setError(null);

    try {
      const result = await deleteAccount({ 
        reason: reason.trim(),
        confirmationText: confirmationText 
      });
      
      if (result.success) {
        setSuccess(true);
        // Redirect to homepage after deletion
        setTimeout(() => {
          window.location.href = '/?message=account-deleted';
        }, 5000);
      } else {
        setError(result.error || 'A aparut o eroare la stergerea contului.');
      }
    } catch (err: any) {
      console.error('Account deletion error:', err);
      setError('A aparut o eroare neasteptata. Va rugam sa Incercati din nou.');
    } finally {
      setIsDeleting(false);
    }
  };

  if (!isLoaded) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
            <Trash2 className="h-6 w-6 text-red-600" />
          </div>
          <CardTitle className="text-red-600">Stergere Definitiva Cont</CardTitle>
          <CardDescription>
            Aceasta actiune este permanenta si nu poate fi anulata
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                Contul a fost sters cu succes. Va multumim ca ati folosit serviciile noastre. 
                Veti fi redirectionat la pagina principala in 5 secunde.
              </AlertDescription>
            </Alert>
          )}

          {!success && !showFinalConfirmation && (
            <>
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>ATENTIE: Aceasta actiune este PERMANENTA!</strong>
                  <ul className="mt-2 space-y-1 list-disc list-inside text-sm">
                    <li>Toate datele dvs. vor fi sterse definitiv</li>
                    <li>Istoricul comenzilor va fi anonimizat</li>
                    <li>Nu veti mai putea accesa contul</li>
                    <li>Adresele de email nu vor mai putea fi folosite</li>
                    <li>Aceasta actiune NU poate fi anulata</li>
                  </ul>
                </AlertDescription>
              </Alert>

              <div className="space-y-4">
                <div>
                  <Label htmlFor="reason">Motivul stergerii (obligatoriu)</Label>
                  <Textarea
                    id="reason"
                    placeholder="Va rugam sa ne spuneti de ce doriti sa stergeti contul..."
                    value={reason}
                    onChange={(e) => setReason(e.target.value)}
                    className="mt-2"
                    rows={3}
                    required
                  />
                </div>

                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="understand"
                      checked={confirmations.understand}
                      onCheckedChange={(checked) => 
                        setConfirmations(prev => ({ ...prev, understand: !!checked }))
                      }
                    />
                    <Label htmlFor="understand" className="text-sm">
                      Inteleg ca aceasta actiune este permanenta
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="dataLoss"
                      checked={confirmations.dataLoss}
                      onCheckedChange={(checked) => 
                        setConfirmations(prev => ({ ...prev, dataLoss: !!checked }))
                      }
                    />
                    <Label htmlFor="dataLoss" className="text-sm">
                      Inteleg ca toate datele mele vor fi sterse definitiv
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="permanent"
                      checked={confirmations.permanent}
                      onCheckedChange={(checked) => 
                        setConfirmations(prev => ({ ...prev, permanent: !!checked }))
                      }
                    />
                    <Label htmlFor="permanent" className="text-sm">
                      Inteleg ca nu voi mai putea accesa contul dupa stergere
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="noRecovery"
                      checked={confirmations.noRecovery}
                      onCheckedChange={(checked) => 
                        setConfirmations(prev => ({ ...prev, noRecovery: !!checked }))
                      }
                    />
                    <Label htmlFor="noRecovery" className="text-sm">
                      Inteleg ca aceasta actiune NU poate fi anulata
                    </Label>
                  </div>
                </div>

                <div className="flex space-x-3">
                  <Button 
                    variant="destructive"
                    onClick={() => setShowFinalConfirmation(true)}
                    disabled={!canProceed}
                    className="flex-1"
                  >
                    Continua cu stergerea
                  </Button>

                  <Button 
                    variant="outline" 
                    onClick={() => {
                      window.close();
                      router.push('/account/settings?tab=security');
                    }}
                    className="flex-1"
                  >
                    Anuleaza
                  </Button>
                </div>
              </div>
            </>
          )}

          {!success && showFinalConfirmation && (
            <>
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>CONFIRMAREA FINALA</strong><br />
                  Pentru a confirma stergerea, tastati exact: <code className="bg-red-100 px-1 rounded">STERGE CONTUL MEU</code>
                </AlertDescription>
              </Alert>

              <div className="space-y-4">
                <div>
                  <Label htmlFor="confirmation">Confirmarea stergerii</Label>
                  <Input
                    id="confirmation"
                    type="text"
                    placeholder="Tastati: STERGE CONTUL MEU"
                    value={confirmationText}
                    onChange={(e) => setConfirmationText(e.target.value)}
                    className="mt-2"
                  />
                </div>

                <div className="flex space-x-3">
                  <Button 
                    variant="destructive"
                    onClick={handleDelete}
                    disabled={isDeleting || !canDelete}
                    className="flex-1"
                  >
                    {isDeleting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Se sterge...
                      </>
                    ) : (
                      'STERGE DEFINITIV CONTUL'
                    )}
                  </Button>

                  <Button 
                    variant="outline" 
                    onClick={() => setShowFinalConfirmation(false)}
                    className="flex-1"
                  >
                    Inapoi
                  </Button>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
