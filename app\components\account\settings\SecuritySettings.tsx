"use client";

import { useState, useTransition } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import {
  Loader2,
  CheckCircle,
  AlertCircle,
  Shield,
  Smartphone,
  AlertTriangle,
  Activity
} from "lucide-react";
import { securitySettingsSchema, type SecuritySettingsInput } from "@/lib/zod";
import { updateSecuritySettings, type ActionResult } from "@/app/actions/account";
import { UserSecurityInfo, UserAuditLogEntry } from "@/app/getData/account-settings";
import { formatDate } from "@/lib/utils";

interface SecuritySettingsProps {
  securityInfo: UserSecurityInfo | null;
  auditLogs: UserAuditLogEntry[];
  ssoProvider?: string | null;
}

export default function SecuritySettings({ securityInfo, ssoProvider }: SecuritySettingsProps) {
  const [isPending, startTransition] = useTransition();
  const [result, setResult] = useState<ActionResult | null>(null);

  const {
    handleSubmit,
    watch,
    formState: { isDirty }
  } = useForm<SecuritySettingsInput>({
    resolver: zodResolver(securitySettingsSchema),
    defaultValues: {
      twoFactorEnabled: securityInfo?.twoFactorEnabled || false,
    }
  });

  const watchedValues = watch();

  const onSubmit = (data: SecuritySettingsInput) => {
    startTransition(async () => {
      try {
        const result = await updateSecuritySettings(data);
        setResult(result);
        
        if (result.success) {
          setTimeout(() => setResult(null), 3000);
        }
      } catch (error) {
        setResult({
          success: false,
          error: "A aparut o eroare neasteptata. Va rugam sa incercati din nou."
        });
      }
    });
  };

  return (
    <div className="space-y-6">
      {/* Security Settings Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Setari Securitate
          </CardTitle>
          <CardDescription>
            Gestionati setarile de securitate pentru a va proteja contul
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Success/Error Messages */}
            {result && (
              <Alert variant={result.success ? "default" : "destructive"}>
                {result.success ? (
                  <CheckCircle className="h-4 w-4" />
                ) : (
                  <AlertCircle className="h-4 w-4" />
                )}
                <AlertDescription>
                  {result.success 
                    ? "Setarile de securitate au fost actualizate cu succes!" 
                    : result.error
                  }
                </AlertDescription>
              </Alert>
            )}

            {/* Two-Factor Authentication */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Smartphone className="h-5 w-5 text-muted-foreground" />
                  <div className="space-y-1">
                    <Label htmlFor="twoFactorEnabled" className="text-base font-medium">
                      Autentificare cu doi factori (2FA)
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      {ssoProvider
                        ? `2FA este gestionat de ${ssoProvider === 'oauth_microsoft' ? 'Microsoft' : ssoProvider === 'oauth_google' ? 'Google' : ssoProvider === 'oauth_facebook' ? 'Facebook' : ssoProvider }`
                        : "Adaugati un nivel suplimentar de securitate la contul dvs."
                      }
                    </p>
                  </div>
                </div>
                {ssoProvider ? (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const urls = {
                        google: 'https://myaccount.google.com/security',
                        microsoft: 'https://account.microsoft.com/security',
                        facebook: 'https://www.facebook.com/settings?tab=security',
                      };
                      window.open(urls[ssoProvider as keyof typeof urls] || '#', '_blank');
                    }}
                  >
                    Gestioneaza in {ssoProvider === 'microsoft' ? 'Microsoft' : ssoProvider === 'google' ? 'Google' : ssoProvider === 'facebook' ? 'Facebook' : ssoProvider}
                  </Button>
                ) : (
                  <Button
                    variant="default"
                    size="sm"
                  >
                    Gestioneaza 2FA
                  </Button>
                )}
              </div>

              {watchedValues.twoFactorEnabled && (
                <Alert>
                  <Smartphone className="h-4 w-4" />
                  <AlertDescription>
                    <strong>2FA Activat:</strong> Contul dvs. este protejat cu autentificare cu doi factori. 
                    Veti fi solicitat sa introduceti un cod de verificare la fiecare autentificare.
                  </AlertDescription>
                </Alert>
              )}

              {!watchedValues.twoFactorEnabled && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Recomandare de securitate:</strong> Activati autentificarea cu doi factori 
                    pentru a va proteja contul impotriva accesului neautorizat.
                  </AlertDescription>
                </Alert>
              )}
            </div>

            {/* Submit Button */}
            <div className="flex justify-end">
              <Button 
                type="submit" 
                disabled={isPending || !isDirty}
                className="bg-[#0066B1] hover:bg-[#004d85]"
              >
                {isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Se salveaza...
                  </>
                ) : (
                  "Salveaza setarile"
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Account Management */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center"> 
            <div className="space-y-2">
              <CardTitle className="flex items-center gap-2 text-red-600">
                <AlertTriangle className="h-5 w-5" />
                Stergere definitiva
              </CardTitle>
              <CardDescription>
                Stergeti permanent contul si toate datele asociate.
              </CardDescription>
            </div> 
            <Button
              variant="destructive"
            >
              Sterge cont
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Account Security Overview */}
      {securityInfo && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Prezentare Generala Securitate
            </CardTitle>
            <CardDescription>
              Informatii despre activitatea si securitatea contului dvs.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between">

              <div className="space-y-2">
                <Label className="text-sm font-medium">Ultima autentificare</Label>
                <p className="text-sm text-muted-foreground">
                  {securityInfo.lastLoginAt 
                    ? formatDate(securityInfo.lastLoginAt.toString())
                    : "Niciodata"
                  }
                </p>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">Numarul total de autentificari</Label>
                <p className="text-sm text-muted-foreground">
                  {securityInfo.loginCount.toLocaleString('ro-RO')}
                </p>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">Status 2FA</Label>
                <Badge variant={securityInfo.twoFactorEnabled ? "default" : "destructive"}>
                  {securityInfo.twoFactorEnabled ? "Activat" : "Dezactivat"}
                </Badge>
              </div>
            </div>

            {securityInfo.loginAttempts > 0 && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Atentie:</strong> Au fost detectate {securityInfo.loginAttempts} incercari 
                  de autentificare esuate recente pe contul dvs.
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      )}

      {/* Recent Activity Log */}
      {/* <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Activitate Recenta
          </CardTitle>
          <CardDescription>
            Ultimele 10 actiuni efectuate pe contul dvs.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {auditLogs.length > 0 ? (
            <ScrollArea className="h-[300px] w-full">
              <div className="space-y-3">
                {auditLogs.map((log) => (
                  <div key={log.id} className="flex items-start space-x-3 p-3 rounded-lg border">
                    <div className="flex-shrink-0 mt-1">
                      {getActionIcon(log.action)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <Badge variant={getActionVariant(log.action)} className="text-xs">
                          {log.action}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {formatDate(log.createdAt)}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">
                        {log.entityType}: {log.entityId || 'N/A'}
                      </p>
                      {log.ipAddress && (
                        <p className="text-xs text-muted-foreground">
                          IP: {log.ipAddress}
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          ) : (
            <div className="text-center py-8">
              <Clock className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">Nu exista activitate recenta inregistrata</p>
            </div>
          )}
        </CardContent>
      </Card> */}
    </div>
  );
}
